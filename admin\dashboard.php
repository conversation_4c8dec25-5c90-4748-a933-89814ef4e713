<?php
ob_start();
session_start();
    include 'init.php';

    $pageTitle ='Dashboard';
  

//   start dhashboard page 

 $numUsers = 6;   // number of latesT users 
 
 $latestUsers = getLatest("*", "users", "UserID", $numUsers);
 
 $numItems = 6;    // number of latesT items

 $latestItems = getLatest("*", "items", "Item_ID", $numItems);

 $numComments = 6;
 

?>


<div class="crudscreen">
    
<div class="crud">

    <div class="head">
        <h2>Crud</h2>
        <p>product managment system</p>
    </div>



    <div class="inputs">
        <input type="text" placeholder="Title" id="title">

        <div class="crudprice">
            <input onkeyup="getTotal()" type="number" placeholder="Price" id="price">
            <input onkeyup="getTotal()" type="number" placeholder="Taxes" id="taxes">
            <input onkeyup="getTotal()" type="number" placeholder="Ads" id="ads">
            <input onkeyup="getTotal()" type="number" placeholder="Discount" id="discount">
            <small id="total"></small>
        </div>

        <input type="number" placeholder="Quntity" id="quanty">
        <input type="text" placeholder="Category" id="category">

        <button id="submit">Create</button>


    </div>

    




    <div class="outputs">
        <div class="searchBlock">
            <input onkeyup="searchProd(this.value)" type="text" placeholder="Search" id="search">
            <div class="btnSearch">
                <button onclick="getSearchMood(this.id)" id="searchTitle">Search By Title</button>
                <button onclick="getSearchMood(this.id)" id="searchCat">Search By Category</button>
            </div>
        </div>

        <div id="deleteAll">

        </div>

        <table>
            <tr>
                <th>Id</th>
                <th>Description</th>
                <th>Price</th>
                <th>Taxes</th>
                <th>Ads</th>
                <th>Discount</th>
                <th>Total</th>
                <th>Category</th>
                <th>Update</th>
                <th>Delete</th>
            </tr>

            <tbody id="tableBody">


            
                

            </tbody>




        </table>



    </div>





</div>



</div>





<?php

// end dashboard page 


    include $tpl . 'footer.php';
 

 ob_end_flush();

 ?>