<?php
/*
=================================================
== Template Page
== You can Add | Edit | Delet members from here
=================================================
*/
ob_start();
session_start(); 
$pageTitle = 'Categories';

if (isset($_SESSION['Username'])) {

    include 'init.php';

    // ************************************** page content for dashboard pages

    $do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

    // Star manage page
    
if ($do == 'Manage') { // Manage Members page 

        $sort = 'ASC';
        $sort_array = array('ASC','DESC');

        if(isset($_GET['sort']) && in_array($_GET['sort'], $sort_array)) {
            $sort = $_GET['sort'];
        }

        
        $stmt3 = $con->prepare("SELECT * FROM cat WHERE section = 1 ORDER BY Ordering $sort");
        $stmt3 -> execute();
        $sections = $stmt3->fetchAll(); 


        $stmt2 = $con->prepare("SELECT * FROM cat WHERE parent = 0 ORDER BY Ordering $sort");
        $stmt2 -> execute();
        $cats = $stmt2->fetchAll(); 

        ?>

        <h1 class="text-center">Manage Categories</h1>
        <a class="add-category btn btn-sm btn-primary" href="cat.php?do=Add"><i class="fa fa-plus"></i> Add New Category</a>




        <div class="container categories">

            <div class="panel panel-default">


                <div class="panel-heading">

                    <i class="fa fa-edit"></i> Manage categories

                    <div class="option pull-right">
                        <i class="fa fa-sort"></i> Ordering: [
                        <a class="<?php if($sort == 'ASC') { echo 'active'; } ?>" href="?sort=ASC">Asc</a> |
                        <a class="<?php if($sort == 'DESC') { echo 'active'; } ?>" href="?sort=DESC">Desc</a>]
                        <i class="fa fa-eye"></i>             View: [
                        <span class="active" data-view="full">Full</span> |
                        <span data-view="classic">Clasic</span>]
                    </div> 

                </div>

                <div class="panel-body">

                <?php









                            

                // Display Sections
                foreach($sections as $sec) {
                    echo "<div class='cat'>";
                        echo "<div class='hidden-buttons'>";
                            echo "<a href='cat.php?do=Edit&catid=".$sec['ID']."' class='btn btn-xs btn-primary'><i class='fa fa-edit'></i>Edit</a>";
                            echo "<a href='cat.php?do=Delete&catid=".$sec['ID']."' class='confirm btn btn-xs btn-danger'><i class='fa fa-close'></i>Delete</a>";
                        echo "</div>";

                        echo '<h3>' . $sec['Name'] . ' <span class="mainbadge">Section</span></h3>';

                        echo "<div class='full-view'>";
                            // Section description
                            echo "<p>"; 
                            if($sec['Description'] == '') { 
                                echo 'This Section has no description'; 
                            } else { 
                                echo $sec['Description']; 
                            } 
                            echo "</p>";
                            
                            // Section visibility indicators
                            if ($sec['Visibility'] == 1) { 
                                echo '<span class="visibility cat-span"><i class="fa fa-eye"></i>Hidden</span>';
                            }
                            if ($sec['Allow_Comment'] == 1) { 
                                echo '<span class="commenting cat-span"><i class="fa fa-close"></i>Comment Disabled</span>';
                            }
                            if ($sec['Allow_Ads'] == 1) { 
                                echo '<span class="advertises cat-span"><i class="fa fa-close"></i>Ads Disabled</span>';
                            }
                            

                            // Get Parent Categories in this Section
                            $parentCategories = getAllfrom("*", "cat", "WHERE section = {$sec['ID']}", "AND parent = 0", "Ordering, Name", "ASC");
                            


                            if (!empty($parentCategories)) {
                                echo "<ul class='list-unstyled child-cats'>";
                                echo "<h4 class='child-head'> Categories </h4>";


                                foreach ($parentCategories as $parentCat) {
                                    echo "<li class='subchild-link'>";
                                        echo "<div class='cat-item'>";
                                            echo "<a href='cat.php?do=Edit&catid=" . $parentCat['ID'] . "' > " . $parentCat['Name'] . "</a>";
                                            echo '<span class="badge">Main-Category</span>';
                                            echo "<a href='cat.php?do=Delete&catid=" . $parentCat['ID'] . "' class='show-delete confirm'> Delete </a>";
                                        echo "</div>";

                                        
                                        // Get Sub-Categories for this Parent Category
                                        $subCategories = getAllfrom("*", "cat", "WHERE parent = {$parentCat['ID']}", "AND section = {$sec['ID']}", "Ordering, Name", "ASC");
                                        
                                        if (!empty($subCategories)) {
                                            echo "<h5 class='child-subhead'> Sub-Categories </h5>";
                                            echo "<ul class='list-unstyled sub-cats'>";
                                            
                                            foreach ($subCategories as $subCat) {
                                                echo "<li class='child-link'>";
                                                    echo "<a href='cat.php?do=Edit&catid=" . $subCat['ID'] . "' >" . $subCat['Name'] . "</a>";
                                                    echo '<span class="subbadge">Sub-Category</span>';
                                                    echo "<a href='cat.php?do=Delete&catid=" . $subCat['ID'] . "' class='show-delete confirm'> Delete </a>";
                                                echo "</li>";
                                            }
                                            
                                            echo "</ul>";
                                        }


                                    echo "</li>";
                                }
                                
                                echo "</ul>";
                            } else {
                                echo "<p class='no-items'>No categories in this section yet.</p>";
                            }
                            
                        echo "</div>"; // End full-view
                    echo "</div>"; // End cat
                    
                    echo "<hr>";
                }

                ?>

            </div>
        </div>
        
        <?php

} elseif ($do == 'Add') { 
        ?>

        <h1 class="text-center"> Add New Category/Section</h1>
        <div class="container"> 
            <form action="?do=Insert" method="POST">


            <div class="dataInpunt">


                <!-- Start of Name field  -->
                <div class="form-group">
                    <label>Name</label>
                    <div class="inputname">
                        <input type="text" 
                        name="name" 
                        class="form-control" 
                        autocomplete="off" 
                        required="required"  
                        placeholder="Name of the Category or Section." />
                    </div>
                </div>
                <!-- End of Name field  -->

            
                <!-- Start of Section field  -->
                <!-- <div class="form-group">
                    <label>This is a Section head</label>
                    <div class="inputname">
                        <div>
                            <input id="sec-yes" type="radio" name="section" value="0" checked />
                            <label for="sec-yes">No</label>
                        </div>
                        
                        <div>
                            <input id="sec-no" type="radio" name="section" value="1" />
                            <label for="sec-no">Yes</label>
                        </div>

                    </div>
                </div> -->
                <!-- End of Section field  -->


                <!-- Start of Description field  -->
                
                <div class="form-group">
                    <label>Description</label>
                    <div class="inputname">
                        <input type="text"
                        name="description" 
                        class="form-control" 
                        required="required"
                        placeholder="Descripe the Category."/>
                    </div>
                </div>
                <!-- End of Description field  -->


                <!-- Start of Ordering field  -->
                <div class="form-group">
                    <label>Ordering</label>
                    <div class="inputname">
                        <input type="text" 
                        name="ordering" 
                        class="form-control" 
                        placeholder="Number to arrange the Category."/>
                    </div>
                </div>
                <!-- End of Ordering field  -->


                <div class="indentity">


                    <!-- Start of Section field  -->
                    <!-- if the section field is 1 and parent is 0 then this item is Section, 
                    if not 0 then the number in this field will be a ID for another category -->

                    <div class="form-group">
                        <h6>Add this Category to a Section</h6>

                        <label>Section</label>
                        <div class="inputname">
                            <select name="section">
                            <option value="0">Not Section</option>
                            <option value="1">Section</option>
                                <?php
                                $allCats = getAllfrom("*", "cat", "WHERE section = 1", "", "ID", "ASC");
                                foreach($allCats as $cat) {
                                    echo "<option value='" . $cat['ID'] . "'>" . $cat['Name'] . "</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>

                    <!-- End of Section field  -->


                    <!-- Start of Parent Type  -->
                    <!-- if the parent field is 0 then this item is parent, 
                    if not 0 then the number in this field will be a ID for another category -->

                    <div class="form-group">
                        <h6>Add this Category to a Parent</h6>

                        <label>Parent</label>
                        <div class="inputname">
                            <select name="parent">
                                <option value="0">None</option>
                                <?php
                                $allCats = getAllfrom("*", "cat", "WHERE parent = 0", "AND section != 1", "ID", "ASC");
                                foreach($allCats as $cat) {
                                    echo "<option value='" . $cat['ID'] . "'>" . $cat['Name'] . "</option>";
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <!-- End of Parent field  -->


                </div>


                <div class="conditionarea">

                

                    <!-- Start of Visibility field  -->
                    <div class="form-group">
                        <label>Visible</label>
                        <div class="inputname">
                            <div>
                                <input id="vis-yes" type="radio" name="visibility" value="0" checked />
                                <label for="vis-yes">Yes</label>
                            </div>
                            
                            <div>
                                <input id="vis-no" type="radio" name="visibility" value="1" />
                                <label for="vis-no">No</label>
                            </div>
                        </div>
                    </div>
                    <!-- End of Visibility field  -->


                    <!-- Start of Commenting field  -->
                        
                    <div class="form-group">
                        <label>Allow Commenting</label>
                        <div class="inputname">
                            <div>
                                <input id="com-yes" type="radio" name="Commenting" value="0" checked />
                                <label for="com-yes">Yes</label>
                            </div>
                            
                            <div>
                                <input id="com-no" type="radio" name="Commenting" value="1" />
                                <label for="com-no">No</label>
                            </div>
                        </div>
                    </div>
                    <!-- End of Commenting field  -->
                    


                    <!-- Start of Ads field  -->
                    <div class="form-group">
                        <label>Allow Ads</label>
                        <div class="inputname">
                            <div>
                                <input id="ads-yes" type="radio" name="ads" value="0" checked />
                                <label for="ads-yes">Yes</label>
                            </div>
                            
                            <div>
                                <input id="ads-no" type="radio" name="ads" value="1" />
                                <label for="ads-no">No</label>
                            </div>
                        </div>
                    </div>
                    <!-- End of Ads field  -->

                </div>


            </div>
            
                




                <!-- Start of Save/submit button  -->
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                        <input type="submit" value="Add Category" class="btn btn-primary btn-lg" />
                    </div>
                </div>
                <!-- End of Save/submit button  -->
            </form>
        </div>


    <?php

} elseif ($do == 'Insert') {
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {

        echo "<h1 class='text-center'>Insert Category</h1>";
        echo "<div class='container'>";

        //Get variables from the form                   
        
        $name    = $_POST['name'];
        $desc    = $_POST['description'];
        $order   = $_POST['ordering'];
        $section  = $_POST['section'];
        $parent  = $_POST['parent'];
        $visible = $_POST['visibility'];
        $comment = $_POST['Commenting'];
        $ads     = $_POST['ads'];

        // check if Category exist in database

        $check = checkItem("Name","cat",$name);

        if ($check == 1) {
        
        $theMsg = '<div class="alert alert-danger">Sorry! This Category is Exist </div>';

        redirectHome($theMsg, 'back', 3);

        } else {
        
        // Insert Category info into Database

                $stmt = $con->prepare("INSERT INTO cat(Name, Description, Ordering, section, parent, Visibility, Allow_Comment, Allow_Ads) 
                VALUES(:zname, :zdesc, :zorder, :zsection, :zparent, :zvisible, :zcomment,:zads) ");
                $stmt->execute(array(

                    'zname' => $name, 
                    'zdesc' => $desc, 
                    'zorder' => $order, 
                    'zsection' => $section, 
                    'zparent' => $parent, 
                    'zvisible' => $visible,
                    'zcomment' => $comment,
                    'zads' => $ads
                ));
                                
                // Echo success message

                $theMsg = '<div class="alert alert-success">' . $stmt->rowCount() . ' Record Inserted </div>';   // send new value to database

                redirectHome ($theMsg, 'back');
        }
        

        // update the database with the new info

        } else {

            echo "<div class='container'>";

            $theMsg = '<div class="alert alert-danger">Sorry!!.. You cant browse this page directly</div>';

            redirectHome($theMsg, 'back');

            echo "</div>";
        }

        echo "</div>";

} elseif ($do == 'Edit') {

    // check if get request catid is numeric & Get the integer value of it
    $catid = isset($_GET['catid']) && is_numeric($_GET['catid']) ? intval($_GET['catid']) : 0;
        
    // get all user data from data base depending on this ID
    $stmt = $con->prepare("SELECT * FROM cat WHERE ID = ? ");
    
    // Execute query
    $stmt->execute(array($catid));
    
    // fetch the data
    $cat = $stmt->fetch();

    // the row count
    $count = $stmt->rowCount();

    // show the form
    if ($count > 0) {   
        ?>
    
        <h1 class="text-center"> Edit Category</h1>
        <div class="container"> 
        
            <form action="?do=Update" method="POST">
                <input type="hidden" name="catid" value="<?php echo $catid ?>">




                    <div class="dataInpunt">


                        <!-- Start of Name field  -->
                        <div class="form-group">
                            <label>Name</label>
                            <div class="inptname">
                                <input type="text" 
                                name="name" 
                                class="form-control"
                                required="required"  
                                placeholder="Name of the Category." 
                                value="<?php echo $cat['Name'] ?>" />
                            </div>
                        </div>
                        <!-- End of Name field  -->

                    
                        <!-- Start of Description field  -->
            
                        <div class="form-group">
                            <label>Description</label>
                            <div class="inputname">
                                <input type="text" 
                                name="description" 
                                class="form-control" 
                                required="required"
                                placeholder="Descripe the Category." 
                                value="<?php echo $cat['Description'] ?>" />
                            </div>
                        </div>
                        <!-- End of Description field  -->
                        
                        <!-- Start of Ordering field  -->
                        
                        <div class="form-group">
                            <label>Ordering</label>
                                <div class="inputname">
                                    <input type="text" 
                                    name="ordering" 
                                    class="form-control" 
                                    placeholder="Number to arrange the Category." 
                                    value="<?php echo $cat['Ordering'] ?>" />
                                </div>
                            </div>
                        <!-- End of Ordering field  -->


                        <div class="indentity">





                            
                            <!-- Start of Section field  -->
                            <!-- if the section field is 1 and parent is 0 then this item is Section, 
                            if not 0 then the number in this field will be a ID for another category -->

                            <div class="form-group">
                                <h6>Change this Category to a Section</h6>

                                <label>Section</label>
                                <div class="inputname">
                                <select name="section">
                                <option value="0" <?php if($cat['section'] == 0) { echo 'selected'; } ?>>Not Section</option>
                                <option value="1" <?php if($cat['section'] == 1) { echo 'selected'; } ?>>Section</option>
                                <?php
                                $allCats = getAllfrom("*", "cat", "WHERE section = 1", "", "ID", "ASC");
                                foreach($allCats as $c) {
                                    // Skip the current category to avoid self-reference
                                    if($c['ID'] != $catid) {
                                        echo "<option value='" . $c['ID'] . "'";
                                        if($cat['section'] == $c['ID']) { echo ' selected'; }
                                        echo ">" . $c['Name'] . "</option>";
                                    }
                                }
                                ?>
                            </select>
                                </div>
                            </div>
                            <!-- End of Section field  -->

                            <!-- Start of Category Type  -->
                            <div class="form-group">
                                <h6>Change this Category to a Parent</h6>

                                <label>Parent</label>
                                <div class="inputname">
                                    <select name="parent">
                                        <option value="0">None</option>
                                        <?php
                                        $allCats = getAllfrom("*", "cat", "WHERE parent = 0", "AND section != 1", "ID", "ASC");
                                        foreach($allCats as $c) {
                                            
                                            echo "<option value='" . $c['ID'] . "'";

                                            if($cat['parent'] == $c['ID']) { echo 'selected';}

                                            echo ">" . $c['Name'] . "</option>";
                                        }

                                        ?>
                                    </select>

                                </div>
                            </div>

                            <!-- End of Category Type  -->
                    


                        </div>

                        <div class="conditionarea">
                                                        
                            <!-- Start of Visibility field  -->
                            
                            <div class="form-group">
                                <label>Visible</label>
                                <div class="inputname">
                                    <div>
                                        <input id="vis-yes" type="radio" name="visibility" value="0" <?php if($cat['Visibility'] == 0) { echo 'checked';} ?>/>
                                        <label for="vis-yes">Yes</label>
                                    </div>
                                    
                                    <div>
                                        <input id="vis-no" type="radio" name="visibility" value="1" <?php if($cat['Visibility'] == 1) { echo 'checked';} ?> />
                                        <label for="vis-no">No</label>
                                    </div>
                                </div>
                            </div>
                            <!-- End of Visibility field  -->


                            <!-- Start of Commenting field  -->
                                
                            <div class="form-group">
                                <label>Allow Commenting</label>
                                <div class="inputname">

                                    <div>
                                        <input id="com-yes" type="radio" name="commenting" value="0"  <?php if($cat['Allow_Comment'] == 0) { echo 'checked';} ?> />
                                        <label for="com-yes">Yes</label>
                                    </div>
                                    
                                    <div>
                                        <input id="com-no" type="radio" name="commenting" value="1" <?php if($cat['Allow_Comment'] == 1) { echo 'checked';} ?> />
                                        <label for="com-no">No</label>
                                    </div>
                                </div>
                            </div>
                            <!-- End of Commenting field  -->

                            <!-- Start of Ads field  -->
                            <div class="form-group">
                                <label>Allow Ads</label>
                                <div class="inputname">
                                    <div>
                                        <input id="ads-yes" type="radio" name="ads" value="0" <?php if($cat['Allow_Ads'] == 0) { echo 'checked';} ?> />
                                        <label for="ads-yes">Yes</label>
                                    </div>
                                    
                                    <div>
                                        <input id="ads-no" type="radio" name="ads" value="1" <?php if($cat['Allow_Ads'] == 1) { echo 'checked';} ?> />
                                        <label for="ads-no">No</label>
                                    </div>
                                </div>
                            </div>
                            <!-- End of Ads field  -->


                            
                        </div>


                    </div>












                    
                    <!-- Start of Save/submit button  -->
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                        <input type="submit" value="Save Changes" class="btn btn-primary btn-lg" />
                        </div>
                    </div>
                    <!-- End of Save/submit button  -->

            </form>

        </div>
        
        <?php
    
    // if there is no such ID, show Error message

        } else {

            echo "<div class='container'>";

            $theMsg = '<div class="alert alert-danger">Sorry!!.. There is no such ID</div>';

            redirectHome($theMsg);

            echo "</div>";
            
            }
} elseif ($do == 'Update') {

        echo "<h1 class='text-center'>Update Category </h1>";
        echo "<div class='container'>";

        if($_SERVER['REQUEST_METHOD'] == 'POST') {

            //Get variables from the form

            $id = $_POST['catid'];
            $name = $_POST['name'];
            $desc = $_POST['description'];
            $order = $_POST['ordering'];
            $section = $_POST['section'];
            $parent = $_POST['parent'];
            $visible = $_POST['visibility'];
            $comment = $_POST['commenting'];
            $ads = $_POST['ads']; 
            

            
            // Update the Database with this info

                $stmt = $con->prepare("UPDATE cat SET Name = ?, Description = ?, Ordering = ?, section = ?, parent = ?, Visibility = ?, Allow_Comment = ?, Allow_Ads = ?  WHERE ID = ?"); // send new value to database
                $stmt->execute(array($name, $desc, $order, $section, $parent, $visible, $comment, $ads, $id));   // send new value to database

            // Echo success message

            $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount().' Record Updated </div>';   // send new value to database

            redirectHome($theMsg, 'back');

            // }                 

        } else {
            
            $theMsg = '<div class="alert alert-danger">  Sorry!!.. You cant browse this page directly </div>'; 

            redirectHome($theMsg);
            
        }

        echo "</div>";

} elseif ($do == 'Delete') {

        echo "<h1 class='text-center'>Delete Category</h1>";
        echo "<div class='container'>";
        
    // check if get request catid is numeric & Get the integer value of it

    $catid = isset($_GET['catid']) && is_numeric($_GET['catid']) ? intval($_GET['catid']) : 0;
        
    // get all user data from data base depending on this ID

    $check = checkItem('ID', 'cat', $catid);
    
    // if there's such ID shoe the form
    
    if ($check > 0) {  
        
        $stmt = $con->prepare("DELETE FROM cat WHERE ID = :zid");
        
        $stmt->bindParam(":zid", $catid);
        
        $stmt->execute();
        
        $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount().' Record Deleted </div>';   // send new value to database
    
        redirectHome($theMsg, 'back');
        
    } else {

        $theMsg = '<div class="alert alert-danger">This ID is not exist</div>';

        redirectHome($theMsg);

        }
    
    

        echo '</div';


    }



    
    
    include $tpl . 'footer.php';

} else {
    header('location: index.php');
    exit();
}

ob_end_flush();   // relase the output

?>
