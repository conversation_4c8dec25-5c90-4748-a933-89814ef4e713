<?php
ob_start();
session_start();
if (isset($_SESSION['Username'])) {

    $pageTitle ='Dashboard';
  
    include 'init.php';

//   start dhashboard page 

 $numUsers = 6;   // number of latesT users 
 
 $latestUsers = getLatest("*", "users", "UserID", $numUsers);
 
 $numItems = 6;    // number of latesT items

 $latestItems = getLatest("*", "items", "Item_ID", $numItems);

 $numComments = 6;
 

?>

    <div class="container home-stats">

        <div class="container text-center">
            <h1>Dashboard</h1>

            <div class="row">

                <div class="col-md-3">
                    <div class="stat st-members">
                        <i class="fa fa-users"></i>
                        <div class="info">
                        Total members 
                        <span><a href="members.php"><?php echo countItems('UserID', 'users')?></a></span>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="stat st-pending">
                        <i class="fa fa-user-plus"></i>
                        <div class="info">
                        Pending members
                        <span><a href="members.php?do=Manage&page=Pending">
                            <?php echo checkItem("RegStatus", "users", 0)  ?>
                        </a></span>
                        </div>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="stat st-items">
                        <i class="fa fa-tag"></i>
                        <div class="info">
                        Total items 
                        <span><a href="items.php"><?php echo countItems('Item_ID', 'items')?></a></span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stat st-comments">
                        <i class="fa fa-comments"></i>
                        <div class="info">
                        Total comments 
                        <span>
                            <a href="comments.php"><?php echo countItems('c_id', 'comments')?></a>
                        </span>

                </div>

            </div>
        
        </div>

        
        <div class="container latest">
        
            <div class="row">

                <div class="col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <i class="fa fa-users"></i> Latest <?php echo $numUsers ?> registered users
                        <span class="toggle-info pull-right">
                            <i class="fa fa-plus fa-lg"></i>
                        </span>
                        </div>
                        <div class="panel-body">
                            <ul class="list-unstyled latest-users">
                                                        
                                <?php
                                if (! empty($latestUsers)){
                                foreach ($latestUsers as $user) {
                                    echo '<li>';  
                                        echo $user ['Username'];
                                        echo '<a href="members.php?do=Edit&userid=' . $user['UserID'] . '">';
                                        echo '<span class="btn btn-success pull-right">'; 
                                            echo '<i class="fa fa-edit"></i> Edit ';

                                            if ($user['RegStatus'] == 0){
                                                echo "<a href='members.php?do=Activate&userid=" . $user['UserID'] . "' 
                                                class='btn btn-info pull-right activate'>
                                                <i class='fa fa-close'></i> Activate </a>";
                                        }

                                        echo '</span>';
                                        echo '</a>';
                                    echo '</li>';

                                    }
                                    
                                } else {
                                    echo 'there is no record to show';
                                }
                        
                            ?>
                            </ul>
                        </div>
                    </div>
                </div>


                <div class="col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <i class="fa fa-tag"></i> Latest <?php echo $numItems ?> items
                            <span class="toggle-info pull-right">
                            <i class="fa fa-plus fa-lg"></i>
                        </span>
                        </div>
                        <div class="panel-body">
                        
                            <ul class="list-unstyled latest-users">
                                
                                <?php
                                if(! empty($latestItems)) {
                                foreach ($latestItems as $item) {
                                    echo '<li>';  
                                        echo $item ['Name'];
                                        echo '<a href="items.php?do=Edit&itemid=' . $item['Item_ID'] . '">';
                                        echo '<span class="btn btn-success pull-right">'; 
                                            echo '<i class="fa fa-edit"></i> Edit ';

                                            if ($item['Approve'] == 0){
                                                echo "<a href='items.php?do=Approve&itemid=" . $item['Item_ID'] . "' class='btn btn-info pull-right activate'><i class='fa fa-check'></i> Activate </a>";
                                        
                                            }

                                        echo '</span>';
                                        echo '</a>';
                                    echo '</li>';
                                }
                            } else {
                                echo 'there is no items to show';
                            }
                            ?>
                            </ul>
                        </div>
                    </div>
                </div>
                
            </div>
            
        </div>


            
        <div class="container latest">
        
            <div class="row">
                <div class="col-sm-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                                <i class="fa fa-comments-o"></i>
                                Latest <?php echo $numComments ?> comments 
                                <span class="toggle-info pull-right">
                                <i class="fa fa-plus fa-lg"></i>
                                </span>
                            </div>
                            <div class="panel-body">
                                <?php
                                    $stmt = $con->prepare("SELECT
                                                            comments.*, users.Username AS Member
                                                        FROM
                                                            comments
                                                        INNER JOIN
                                                            users
                                                        ON
                                                            users.UserID = comments.user_id
                                                        ORDER BY
                                                            c_id DESC
                                                        LIMIT $numComments");
                                    $stmt->execute();
                                    $comments = $stmt->fetchAll();
                                
                                    if(! empty($comments)) {
                                    foreach ($comments as $comment) {
                                        echo '<div class="comment-box">';
                                            echo '<span class="member-n">
                                            <a href="members.php?do=Edit&userid=' . $comment['user_id'] . '">

                                            ' . $comment['Member'] . '</a></span>';
                                            echo '<p class="member-c">' . $comment['comment'] . '</p>';
                                        echo '</div>';
                                    }

                                } else {
                                    echo 'there is no comments to show';
                                    
                                }
                                    
                                ?>

                            </div>
                        </div>

                    </div>
                </div>
            </div>

        </div>
            <!-- end comment section  -->
    </div>

<?php

// end dashboard page 


    include $tpl . 'footer.php';
 
} else {
    header('Location: index.php');   // redirect to dashboard page

    exit();
 }

 ob_end_flush();

 ?>