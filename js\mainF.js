// "use strict";


// let category_nav_list = document.querySelector(".category_nav_list");

// var cart = document.querySelector(".cart");



// function Open_Categ_list(){
//     category_nav_list.classList.toggle("active")
// }

// let nav_links = document.querySelector(".nav_links")

// function open_Menu() {
// nav_links.classList.toggle("active")
// }



// Define open_close_cart function in the global scope
window.open_close_cart = function() {
    const cartElement = document.querySelector('.cart');
    if (cartElement) {
        cartElement.classList.toggle('active');
        if (cartElement.classList.contains('active')) {
            updateCart(); // This calls updateCart
        }
    }
};

// Define updateCart function before it's used
function updateCart() {
    const cartItemsContainer = document.getElementById("cart_items");
    if (!cartItemsContainer) return; // Exit if cart container doesn't exist
    
    const cart = JSON.parse(localStorage.getItem("cart")) || [];
    const checkout_items = document.getElementById("checkout_items");

    let items_input = document.getElementById("items");
    let total_Price_input = document.getElementById("total_Price");
    let count_Items_input = document.getElementById("count_Items");

    // Check if cart is empty
    if (cart.length === 0) {
        // Display empty cart message
        cartItemsContainer.innerHTML = '<div class="empty-cart">Your cart is empty</div>';
        
        // Update checkout page if on checkout
        if (checkout_items) {
            checkout_items.innerHTML = '<div class="empty-cart">Your cart is empty</div>';
            
            // Update hidden inputs
            if (items_input) items_input.value = "";
            if (total_Price_input) total_Price_input.value = "0";
            if (count_Items_input) count_Items_input.value = "0";
            
            // Update totals
            const subtotal_checkout = document.querySelector(".subtotal_checkout");
            const total_checkout = document.querySelector(".total_checkout");
            
            if (subtotal_checkout) subtotal_checkout.innerHTML = "$0";
            if (total_checkout) total_checkout.innerHTML = "$0";
        }
        
        return; // Exit early
    }

    // Only try to update checkout form inputs if they exist
    if(checkout_items && items_input && total_Price_input && count_Items_input){
        checkout_items.innerHTML = "";
        items_input.value = "";
        total_Price_input.value = "";
        count_Items_input.value = "";
    }

    var total_Price = 0;
    var total_count = 0;
    var total_count_header = 0;
    
    // Define totalPriceView outside the loop with a default value
    var totalPriceFormating = new Intl.NumberFormat("en-EG", {style: 'currency', currency: 'EGP'});
    var totalPriceView = totalPriceFormating.format(0);

    cartItemsContainer.innerHTML = "";

    cart.forEach((item, index) => {
        let total_Price_item = item.Price * item.quantity;
        
        // Total cart price formatting 
        total_Price += total_Price_item;
        totalPriceView = totalPriceFormating.format(total_Price);
        
        // Unit price formatting
        unitPrice = item.Price;
        unitPriceFormating = new Intl.NumberFormat("en-EG",{style: 'currency', currency: 'EGP'});
        unitPriceView = unitPriceFormating.format(unitPrice);

        // Unit item total price formatting
        unitItemTotalPrice = total_Price_item;
        unitItemTotalPriceFormating = new Intl.NumberFormat("en-EG",{style: 'currency', currency: 'EGP'});
        unitItemTotalPriceView = unitItemTotalPriceFormating.format(unitItemTotalPrice);
        
        total_count += item.quantity;
        total_count_header += 1;

        // Only update checkout form inputs if they exist
        if(items_input && total_Price_input && count_Items_input) {
            items_input.value += item.Name + "   ---   " + "price : " + total_Price_item + "   ---   " + "Item ID : " + item.Item_ID + "   ---   " + "count : " + item.quantity + "\n";
            console.log(items_input.value);
            total_Price_input.value = total_Price + 20;
            count_Items_input.value = total_count;
        }

        // Cart items display
        cartItemsContainer.innerHTML += `
            <div class="item_cart">
                <img src="admin/uploads/items/${item.pic1}" alt="">
                <div class="content">
                    <h4>${item.Name}</h4>
                    <p class="price_cart">
                        <span class="unit-price">${unitPriceView}</span>  
                    </p>
                    <div class="quantity_control">
                        <button class="decrease_qyantity" data-index="${index}">-</button>
                        <input type="number" class="quantity_input" value="${item.quantity}" readonly>
                        <button class="increase_qyantity" data-index="${index}">+</button>
                    </div>
                    <span class="total-price">${unitItemTotalPriceView}</span>
                </div>
                <button class="delete_item" data-index="${index}">
                    <i class="fa-solid fa-trash-can"></i>
                </button>
            </div>
        `;

        // Add checkout items display - THIS IS THE MISSING PART
        if(checkout_items){
            checkout_items.innerHTML += `
                <div class="item_cart">
                    <div class="image_name">
                        <img src="admin/uploads/items/${item.pic1}" alt="">
                        <div class="content">
                            <h4>${item.Name}</h4>
                            <p class="price_cart">
                                <span class="unit-price">${unitPriceView}</span> × ${item.quantity} =
                                <span class="total-price">${unitItemTotalPriceView}</span>
                            </p>
                            <div class="quantity_control">
                                <button class="decrease_qyantity" data-index="${index}">-</button>
                                <span class="quantity">${item.quantity}</span>
                                <button class="increase_qyantity" data-index="${index}">+</button>
                            </div>
                        </div>
                    </div>
                    <button class="delete_item" data-index="${index}">
                        <i class="fa-solid fa-trash-can"></i>
                    </button>
                </div>
            `;
        }
    });

    // Update cart counters and totals
    const price_cart_total = document.querySelector('.price_cart_total');
    const count_item_cart = document.querySelector('.count_item_cart');
    const count_item_header = document.querySelector('.count_item_header');

    if(price_cart_total) price_cart_total.innerHTML = ` ${totalPriceView}`;
    if(count_item_cart) count_item_cart.innerHTML = total_count;
    if(count_item_header) count_item_header.innerHTML = total_count_header;

    // Update checkout totals
    if(checkout_items){
        const subtotal_checkout = document.querySelector(".subtotal_checkout");
        const total_checkout = document.querySelector(".total_checkout");

        if(subtotal_checkout) subtotal_checkout.innerHTML = `${totalPriceView}`;
        if(total_checkout) total_checkout.innerHTML = `${totalPriceFormating.format(total_Price + 20)}`;
    }

    // Add event listeners for cart item buttons
    const increaseButtons = document.querySelectorAll('.increase_qyantity');
    const decreaseButtons = document.querySelectorAll('.decrease_qyantity');
    const deleteButtons = document.querySelectorAll('.delete_item');

    increaseButtons.forEach(button => {
        button.addEventListener('click', (event) => {
            const itemIndex = event.target.getAttribute('data-index');
            increaseQTY(itemIndex);
        });
    });

    decreaseButtons.forEach(button => {
        button.addEventListener('click', (event) => {
            const itemIndex = event.target.getAttribute('data-index');
            decreaseQTY(itemIndex);
        });
    });

    deleteButtons.forEach(button => {
        button.addEventListener('click', (event) => {
            const itemIndex = button.getAttribute('data-index');
            removeFromCart(parseInt(itemIndex, 10));
        });
    });
}

// Function to add product to cart - modified to prevent duplicate additions
function addToCart(product, quantity) {
    // Log for debugging
    console.log(`Adding product ${product.Name} with quantity ${quantity}`);
    
    let cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Check if the product is already in the cart
    const existingProductIndex = cart.findIndex(item => item.Item_ID === product.Item_ID);
    
    if (existingProductIndex !== -1) {
        console.log(`Product already in cart at index ${existingProductIndex}`);
        // Don't update quantity - product is already in cart
        return; // Exit without adding more quantity
    } else {
        // Add the product with exactly quantity 1, ignoring the passed quantity
        console.log(`Adding new product to cart with quantity 1`);
        cart.push({ ...product, quantity: 1 });
    }

    localStorage.setItem('cart', JSON.stringify(cart));
    updateCart();
}

// Define helper functions before they're used
function increaseQTY(index) {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    if (cart[index]) {
        cart[index].quantity += 1;
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCart(); // Refresh the cart display
    }
}

function decreaseQTY(index) {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    if (cart[index] && cart[index].quantity > 1) {
        cart[index].quantity -= 1;
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCart(); // Refresh the cart display
    }
}

function removeFromCart(index) {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const removedProduct = cart.splice(index, 1)[0];
    localStorage.setItem('cart', JSON.stringify(cart));
    updateCart();
    updateCartButtons(); // Ensure buttons are updated
}

function updateCartButtons() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const addToCartButtons = document.querySelectorAll('.btn_add_cart');

    addToCartButtons.forEach(button => {
        const itemId = button.getAttribute('data-id');
        const isInCart = cart.some(item => item.Item_ID == itemId);

        if (isInCart) {
            button.classList.add('active');
            button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Item in cart';
        } else {
            button.classList.remove('active');
            button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Add to cart';
        }
    });
}

// Add this function to handle the expand/collapse functionality for product details
function initProductDetailsToggle() {
    console.log('Initializing product details toggle');
    
    // First check if we're on a product details page
    const descriptionSection = document.querySelector('.description');
    if (!descriptionSection) {
        console.log('Not on a product details page, skipping toggle initialization');
        return; // Exit early if not on a product details page
    }
    
    // Get all elements with class 'has-child' inside the description section
    const detailItems = document.querySelectorAll('.description .has-child');
    
    if (detailItems.length > 0) {
        console.log(`Found ${detailItems.length} detail items to toggle`);
        
        // Add click event listener to each item
        detailItems.forEach((item, index) => {
            const toggleLink = item.querySelector('.icon-small');
            
            if (toggleLink) {
                toggleLink.addEventListener('click', function(e) {
                    e.preventDefault(); // Prevent default link behavior
                    
                    // Toggle the 'expand' class on the parent li element
                    item.classList.toggle('expand');
                    
                    console.log(`Toggled item ${index + 1}, expanded: ${item.classList.contains('expand')}`);
                });
                
                console.log(`Added click listener to item ${index + 1}`);
            } else {
                console.warn(`No toggle link found for item ${index + 1}`);
            }
        });
    } else {
        // Check if we have alternative structure
        const alternativeItems = document.querySelectorAll('.product-details .detail-title');
        if (alternativeItems.length > 0) {
            console.log(`Found ${alternativeItems.length} alternative detail items to toggle`);
            
            alternativeItems.forEach((item, index) => {
                item.addEventListener('click', function(e) {
                    // Toggle the 'active' class on the clicked title
                    this.classList.toggle('active');
                    
                    // Find the next sibling which should be the content
                    const content = this.nextElementSibling;
                    if (content && content.classList.contains('detail-content')) {
                        content.classList.toggle('active');
                        console.log(`Toggled alternative item ${index + 1}`);
                    }
                });
                
                console.log(`Added click listener to alternative item ${index + 1}`);
            });
        } else {
            console.log('No detail items found to toggle');
        }
    }
}

// Call the function when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');
    
    // Only initialize product details toggle on single product pages
    const isProductPage = document.querySelector('.product-details') || 
                          document.querySelector('.description');
    
    if (isProductPage) {
        console.log('On product page, initializing toggle');
        initProductDetailsToggle();
    } else {
        console.log('Not on product page, skipping toggle initialization');
    }
});

// Also call it when the page is fully loaded (including images)
window.addEventListener('load', function() {
    console.log('Page fully loaded');
    
    // Only initialize product details toggle on single product pages
    const isProductPage = document.querySelector('.product-details') || 
                          document.querySelector('.description');
    
    if (isProductPage) {
        console.log('On product page, initializing toggle');
        initProductDetailsToggle();
    } else {
        console.log('Not on product page, skipping toggle initialization');
    }
});

// Now add the event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Clone all add to cart buttons to remove existing event listeners
    const addToCartButtons = document.querySelectorAll('.btn_add_cart');
    addToCartButtons.forEach(button => {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
    });
    
    // Now add a single event listener using event delegation
    document.addEventListener('click', function(event) {
        const button = event.target.closest('.btn_add_cart');
        if (!button) return;
        
        event.preventDefault();
        event.stopPropagation();
        
        const itemId = button.getAttribute('data-id');
        
        // Check if button is already active
        if (button.classList.contains('active')) {
            console.log('Button already active, not adding to cart');
            return;
        }
        
        console.log(`Add to cart clicked for product ID: ${itemId}`);
        
        fetch('products.json')
            .then(response => response.json())
            .then(data => {
                const selectedProduct = data.find(product => product.Item_ID == itemId);
                if (selectedProduct) {
                    // Force quantity 1
                    addToCart(selectedProduct, 1);
                    
                    // Update button state
                    const allMatchingButtons = document.querySelectorAll(`.btn_add_cart[data-id="${itemId}"]`);
                    allMatchingButtons.forEach(btn => {
                        btn.classList.add('active');
                        btn.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Item in cart';
                    });
                }
            });
    });
    
    // Initialize cart and buttons
    updateCart();
    updateCartButtons();
}); // Function to update the state of "Add to cart" buttons based on localStorage in single product page
function updateCartButtons() {
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    const addToCartButtons = document.querySelectorAll('.btn_add_cart');

    addToCartButtons.forEach(button => {
        const itemId = button.getAttribute('data-id');
        const isInCart = cart.some(item => item.Item_ID == itemId);

        if (isInCart) {
            button.classList.add('active');
            button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Item in cart';
        } else {
            button.classList.remove('active');
            button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Add to cart';
        }
    });
}

// Call this function on page load
document.addEventListener('DOMContentLoaded', updateCartButtons);





//         // Get the item ID from the button's data attribute
//         const itemId = button.getAttribute('data-id');

//         // Find the product data (replace with actual product fetching logic)
//         const selectedProduct = { Item_ID: itemId, Name: "Sample Product", Price: 100 }; // Replace with actual product data

//         // Add the product to the cart with the exact quantity
//         addToCart(selectedProduct, quantity);

//         // Update the cart count in the header
//         updateCart();

//         // Disable the button and change its appearance
//         button.classList.add('active');
//         button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Item in cart';

//         console.log(`Added to cart: Item ID = ${itemId}, Quantity = ${quantity}`);
//     }
// });



