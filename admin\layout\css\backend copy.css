
/* Start Main rulez */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: sans-serif;
}
p{
    margin: 0;
}
body {
    /* background-color: #dbd8d8; */
    font-size: 16px;


    /* display: flex; */
    /* justify-content: center; */
    /* align-items: center; */
    /* min-height: 100vh; */
    background: #EAEAEA;

}



.img-box{
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid white;
}
.img-box img{
    width: 100%;
}

.profile{
display: flex;
align-items: center;
gap: 30px;

}
.profile h2{
    font-size: 20px;
    text-transform: capitalize;
}


.menu{
    background-color: wheat;
    width: 60px;
    width: 100vh;
    padding: 20px;
    overflow: hidden;
}
ul{
    list-style: none;
    position: relative;
    height: 95%;
}
ul li a {
    display: block;
    text-decoration: none;
    text-transform: uppercase;
    padding: 10px;
    margin: 10px 0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 40px;
    color: darkblue;
    cursor: pointer;
    transition: all 0.3s ease;
}
ul li a:hover, .active{
    background-color:sienna;
    text-decoration: none;

    color: #fff;
    transform: scale(1.05);
    transition: all 0.3s ease;
}
/* 
ul li i{
    font-size: 20px;
    color: #000;
}
ul li a{
    text-decoration: none;
    color: #000;
}
ul li a:hover{
    color: red
} */


.log-out{
    position: absolute;
    bottom: 0;
    width: 100%;
}
.log-out a {
    background-color: red;
}

ul li a i {
    font-size: 30px;
    color: #000;
}































/* old  */

.asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 28px;
    color: #8f2018;
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:focus,
.navbar-inverse .navbar-nav>.open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}


.dropdown-menu {
    min-width: 180px;
    padding: 0;
    font-size: 1em;
    border: none;
    border-radius: 0;
}

.dropdown-menu>li>a {
    color: #fff;
    padding: 10px 15px;
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
    color: #fff;
    background-color: #8e44ad;
}

.nice-message {
    padding: 10px;
    background-color: #FFF;
    margin: 10px 0;
    border-left: 5px solid #080;
}
.container {
    display: block;
    width: 95%;
    flex-wrap: wrap;
    margin: 10px auto;
    padding: 0;
    align-items: center;
    position: relative;
}
.row {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;

    
}

label {
    font-weight: 600;
    font-size: 14px;
}
.btn-group-sm>.btn, .btn-sm{
    margin: 5px;
}

/* End Main Rulez */


/* start login form */

.login {
    width: 300px;
    margin: 100px auto;
}

.login h4 {

    color: #888;
}

.login input {
    margin-bottom: 10px;
}

.login .form-control {
    background-color: #EAEAEA;
}

.login .btn {
    background-color: blueviolet;
}

.navbar {
    border-radius: 0;
    margin-bottom: 0;
}

.nav>li>a,
.navbar-brand {
    font-size: 1em;
}


.form-control {
    position: relative;

}
.members{
    width: 100%;
}
/*End bootstrap edits */

/* Start Dashboard page */

.home-stats .stat {
    padding: 20px;
    font-size: 15px;
    color: #FFF;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.home-stats .stat i {
    position: absolute;
    font-size: 80px;
    top: 35px;
    left: 30px;
}

.home-stats .stat .info {
    float: right;
}

.home-stats .stat a {
    color: #FFF;
}

.home-stats .stat a:hover {
    text-decoration: none;
}

.home-stats .stat span {
    display: block;
    font-size: 60px;
}

.home-stats .st-members {
    background-color: #3498db;


}

.home-stats .st-pending {
    background-color: #e67e22;



}

.home-stats .st-items {
    background-color: #16a085;

}

.home-stats .st-comments {
    background-color: #8e44ad;

}


.latest {
    margin-top: 30px;
}

.latest .toggle-info {
    color: #999;
    cursor: pointer;

}

.latest .toggle-info:hover {
    color: #444;
}

.latest-users {
    margin-bottom: 0;
}

.latest-users li {
    padding: 10px;
    overflow: hidden;
}

.latest-users li:nth-child(odd) {
    background-color: #EEE;

}

.latest-users .btn-success .latest-users .btn-info {
    padding: 2px 8px;

}

.latest-users .btn-info {
    margin-right: 5px;

}

.latest .comment-box {
    margin: 5px 0 10px;

}

.latest .comment-box .member-n,
.latest .comment-box .member-c {
    float: left;
}

.latest .comment-box .member-n {
    width: 80px;
    text-align: center;
    margin-right: 20px;
    position: relative;
    top: 10px;

}

.latest .comment-box .member-c {
    width: calc(100% - 100px);
    background-color: #EFEFEF;
    padding: 10px;
    position: relative;
}

.latest .comment-box .member-c:before {
    content: "";
    display: block;
    position: absolute;
    left: -28px;
    top: 5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent #EFEFEF transparent transparent;
    border-width: 15px;


}



/* End of Dashboard page */

/* Start Member page */

h1 {
    font-size: 27px;
    margin: 3px 0;
    font-family: bold;
    color: #666;

}

.show-pass {
    position: absolute;
    top: 36px;
    right: -27px;
    color: darkviolet;
}

.manage-members img {
    width: 50px;
    height: 50px;

}
.table-responsive{
    width: 100%;
}

.main-table {
    -webkit-box-shodow: 0 3px 10px #CCC;
    -moz-box-shadow: 0 3px 10px #CCC;
    box-shadow: 0 3px 10px #CCC;
}

.main-table td {
    background-color: #FFF;
    vertical-align: middle !important;


}

.main-table tr:first-child td {
    background-color: #333;
    color: #FFF;

}

.main-table .btn {
    padding: 3px 10px;
    margin: 5px;
}

.activate {
    margin-left: 5px;
}
/* new  */

.profilepic  { 
    display: flex;
    height: 150px;
    background: transparent; /* delete*/
    width: 200px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 5px;
    gap: 5px;
    position: absolute;
    right: 10%;
    top: 35%;

}



.profilepic img{
    position: relative;
    border-radius: 5px;
    margin: 5px;
    width: 100%;
    height: 100%;
    

} 
.profilepic .btn{
    font-size: 12px;
    line-height: 0.5;
    border-radius: 5px;
    margin: 5px;
    background: transparent;
}
.profilepic .btn p{
    color: black;
    font-size: 18px;
    font-weight: 500;
}


.edituser{
    width: 100%;
}

/* End Member page */


/* start product page  */
.flexrow{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
}

.dataInpunt {
    width: 49%;

}
.picInput {
    width: 49%;
    margin-left: 2px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
}


.form-group{
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    margin: 10px 0;
}
.form-group input {
    display: inline-block;
    max-width: 100%;
    font-weight: 700;
    font-size: small;
}
.form-group input {
    padding: 2px 5px;
    margin: 1px 0;
    display: inline-block;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;

}

   
.indentity{
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-evenly;
    align-items: center;
    /* display: inline-block; */
    border-radius: 4px;
    padding: 5px 15px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    margin: 10px 0;

}

.indentity .selectboxit{
    padding: 0 20px;
    margin: 5px 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}
.pricearea{
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-evenly;
    align-items: center;
    /* display: inline-block; */
    gap: 10px;
    border-radius: 4px;
    padding: 5px 15px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    margin: 10px 0;
   
}
.conditionarea{
    display: flex;
    flex-direction: row;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    /* display: inline-block; */
    border-radius: 4px;
    padding: 5px 15px;
    border: 1px solid #ccc;
    box-sizing: border-box;
    margin: 10px 0;
    
}
.conditionarea .selectboxit{
    padding: 2px 20px;
    margin: 1px 0;
    border-radius: 4px;
    border: 1px solid #ccc;
    box-sizing: border-box;
}






.manage-items img{
    width: 50px;
    height: 50px;
    border: 1px solid #050505;
    border-radius: 5px;
    margin: 0 2px;
    cursor: pointer;
    transition: 0.3s ease-in-out;
}
.manage-items img:hover{
    scale: 1.05;
    box-shadow: 0 4px 5px rgba(0, 0, 0, .1);
    border-color: #3362c3;
}

/* add item picture start  try 2  */
.item-pic1-upload {
    background: #222;
    color: #fff;
    background-size: cover;
    max-width: 300px;
    height: 200px;
    margin: auto;
    position: relative;
}
.upload-icon{
    position: absolute;
    width: 60px;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
}
 /* try 3  */

 .hero input{
    height: 100px;
    width: 100px;
 }

 .hero{
    display: flex;
    flex-direction: column;
    align-items: center;
 }
 .hero img {
    width: 180px;
    height: 180px;
    border-radius: 5px;
    margin: 2px;
}
.hero label{
    display: block;
    /* width: 180px; */
    background: #3362c3;
    color: #fff;
    padding: 5px;
    text-align: center;
    border-radius: 5px;
    cursor: pointer;
    align-items: center;
    margin: 5px;
    transition: 0.3s ease-in-out;

}
.hero label:hover{
    background: #fff;
    color: #050505;
    scale: 1.05;
}
 /* .prodcard{
    width: 195px;
    border-radius: 15px;
    text-align: center;
    justify-content: center;
 }
 .prodcard h1{
    font-weight: 500;
    color: #000;
 }
 */
.prodcard input{
    display: none;
}





/*
.itempic{
    max-width: 400px;
    width: 100%;
    padding: 50px;
    border: 2px solid #4dafd6;
    position: absolute;
    top: 115px;
    right: 15px;
    border-radius: 1px;
    text-align: center;
    overflow: hidden;
}

.itempic .icon span.up{
    font-size: 48px;
    color: tomato;
}
.itempic .icon h6.close{
    line-height: 32px;
    width: 32px;
    border: 3px solid tomato;
    border-radius: 50%;
    font-size: 18px;
    color: tomato;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 1;
    cursor: pointer;
    font-weight: 900;
    display: none;
}
.itempic .text{
    font-size: 19px;
    margin-top: 18px;
    margin-bottom: 19px;
    color: #4dafd6;
}
.itempic input{
    display: none;

}

.itempic .upload{
    line-height: 15px;
    width: 200px;
    height: 34px;
    background: #4dafd6;
    color: #fff;
    padding: 8px 20px;
    font-weight: 500;
    font-size: 17px;
    border-radius: 5px;
    cursor: pointer;
}
.itempic .upload:hover{
    box-shadow: 0 4px 5px rgba(0, 0, 0, .1);
}
.itempic .itemimg img{
    width: 99%;
    height: 99%;
    object-fit: cover;
    position: absolute;
    top: 2px;
    left: 2px;
    border-radius: 5px;
    transition: transform .5s ease;
    display: none;
}
.itempic .itemimg:hover img{
    transform: scale(1.1);
}
.itempic .itemimg img.active{
    display: block;
}
.itempic .icon .close.exit{
    display: block;
} 



.itempic{
    max-width: 430px;
    margin: 100px auto;
    background: #fff;
    border-radius: 5px;
    padding: 30px;
    box-shadow:  7px 7px 12px rgba(0, 0, 0, 0.05);
}
.itempic .head-text{
    color: #2a2a2a;
    font-size: 27px;
    font-weight: 600;
    text-align: center;
}
.itempic form{
    height: 167px;
    display: flex;
    cursor: pointer;
    margin: 30px 0;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border-radius: 5px;
    border: 2px solid #22ab79;
}
*/

form :where(i, p){
    color: #22ab79;
}
form i{
    font-size: 50px;
}
form p{
    margin-top: 15px;
    font-size: 16px;
}
section .row{
    margin-bottom: 10px;
    background: #3498db;
    list-style: none;
    padding: 15px 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
section .row i{
    color: #050505;
    font-size: 30px;
}
section .details span {
    font-size: 14px;
}.progress-area .row .content{
    width: 100%;
    margin-left: 15px;
}
.progress-area .content .progress-bar{
    height: 6px;
    width: 100%;
    margin-bottom: 4px;
    background: #fff;
    border-radius: 30px;
}
.content .progress-bar .progress{
    height: 100%;
    width: 0%;
    background: #050505;
    border-radius: inherit;
}
.uploaded-area::-webkit-scrollbar{
    width: 0px;

}
.uploaded-area .row .row .content{
    display: flex;
    align-items: center;
}
.uploaded-area .row .details{
    display: flex;
    margin-left: 15px;
    flex-direction: column;
}
.uploaded-area .row .details .size{
    color: #404040;
    font-size: 11px;
}
.uploaded-area i.fa-check{
    font-size: 16px;
}













/* Start Categories page */


.categories .panel-heading {
    color: #050505;
    font-weight: bold;
}

.categories .panel-heading i {
    position: relative;
    top: 1px;
}

.categories .panel-body {
    padding: 0;
}

.categories .option a {
    color: #888;
    text-decoration: none;
}


.categories .option span {
    color: #888;
    cursor: pointer;
}

.categories .option .active {
    color: #f00;
    font-weight: bold;

}

.categories hr {
    margin-top: 0;
    margin-bottom: 0;
}

.categories .cat {
    padding: 15px;
    position: relative;
    overflow: hidden;
}

.categories .cat:hover {
    background-color: #EEE;
}

.categories .cat:hover .hidden-buttons {
    right: 10px;
}

.categories .cat .hidden-buttons {

    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;
    position: absolute;
    top: 20px;
    right: -120px;
}



.categories .cat .hidden-buttons a {
    margin-right: 5px;
}

.categories .cat h3 {
    margin: 0;
    cursor: pointer;
    font-weight: bold;
    color: #6A6A6A;
}


.categories .cat .full-view p {
    margin: 10px 0;
    color: #707070;
    
}

/* new  */


/* new */

.categories .cat:last-of-type ~ hr {
    display: none;
}


.categories .cat .visibility {
    background-color: #d35400;
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px;
}


.categories .cat .commenting {
    background-color: #2c3e50;
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px;
}

.categories .subchild-link {
    display: flex;
    margin-bottom: 10px;
}

.categories .child-link {
    display: flex;
}

.categories .cat .advertises {
    background-color: #c0392b;
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px;
}

.categories .add-category {
    margin-top: -10px;
    margin-bottom: 30px;
}

.categories .child-head {
    margin: 10px 0 15px 10px;
    font-weight: bold;
    font-size: 16px;
    color: #22ab79;
}

.categories .child-cats {
    margin: 0;
}

.categories .child-cats li {
    margin-left: 15px;
    display: block;
}

.categories .child-cats li:before {
    content: "~";

}

.categories .show-delete {
    color: #f00;
    display: none;

}

/* End  Categories page */

/* Category management styling */

.cat .mainbadge{
    background-color: #2ff706;
    font-size: 18px;
    vertical-align: middle;
    margin: 3px 0px; 
    color: #fff;
    padding: 5px 20px;
    border-radius: 5px;
}
.cat .badge {
    background-color: #92b85d;
    font-size: 12px;
    vertical-align: middle;
    margin: 3px 20px; 
    border-radius: 5px;
}
.cat .subbadge{
    background-color: #3e913e;
    font-size: 8px;
    vertical-align: middle;
    margin: 3px 18px; 
    color: #fff;
    padding: 2px 5px;
    border-radius: 5px;
}

.cat-item {
    display: contents;
    gap: 10px;
    align-items: center;
    margin-bottom: 5px;
}
.child-head {
    margin-top: 15px;
    margin-bottom: 5px;
    color: #666;
    font-weight: bold;
}

.sub-cats {
    margin-left: 20px;
    border-left: 1px solid #eee;
    padding-left: 10px;
    margin-bottom: 40px;
}
.categories .child-head .subcat{
    font-size: 11px;
}
.categories .child-subhead{
    font-size: 12px;
    font-weight: 600;
    color: darkolivegreen;
    margin: 10px 24px;
}
.categories .sub-cats li a{
    font-size: 12px;

}
.no-items {
    color: #999;
    font-style: italic;
    margin-top: 10px;
}





@media (max-width:810px){
    .profilepic {
        position: relative;
        top: 0;
        left: 25%;
        margin-bottom: 90px;
      }
      .form-horizontal.edituser{
        padding-top: 10%;
    }

    
 
}