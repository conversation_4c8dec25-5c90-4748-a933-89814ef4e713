// Google Script URL for form submission
const scriptURL = "https://script.google.com/macros/s/AKfycbxtr7IF7AEMUxVwt7O3dFGrFht5UkjCo0CXtfoDc9Ru8IggNIHXAcG9hIyvfC2elHM/exec";

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Checkout script loaded');
    
    // Get the form element
    const form = document.getElementById("form_contact");
    
    // Check if form exists before adding event listener
    if (form) {
        console.log('Checkout form found, adding submit listener');
        
        // Add submit event listener
        form.addEventListener("submit", function(e) {
            e.preventDefault();
            console.log('Form submitted, sending data...');
            
            // Show loading indicator
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.innerHTML = 'Processing...';
                submitButton.disabled = true;
            }
            
            // Validate form before submission
            if (!validateCheckoutForm(form)) {
                console.log('Form validation failed');
                if (submitButton) {
                    submitButton.innerHTML = 'Place order';
                    submitButton.disabled = false;
                }
                return;
            }
            
            // Submit form data
            fetch(scriptURL, {
                method: "POST",
                body: new FormData(form),
            })
            .then((response) => {
                console.log('Form submitted successfully');
                
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'success-message';
                successMessage.innerHTML = 'Order placed successfully! Redirecting...';
                form.appendChild(successMessage);
                
                // Clear cart and reload page after delay
                setTimeout(() => {
                    localStorage.removeItem("cart");
                    window.location.reload();
                }, 2500);
            })
            .catch((error) => {
                console.error("Error submitting form:", error.message);
                
                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.innerHTML = 'There was an error processing your order. Please try again.';
                form.appendChild(errorMessage);
                
                // Reset button
                if (submitButton) {
                    submitButton.innerHTML = 'Place order';
                    submitButton.disabled = false;
                }
            });
        });
    } else {
        console.warn('Checkout form not found on this page');
    }
    
    // Initialize checkout page
    initCheckoutPage();
});

// Function to validate checkout form
function validateCheckoutForm(form) {
    // Get required fields
    const email = form.querySelector('input[name="email"]');
    const name = form.querySelector('input[name="name"]');
    const address = form.querySelector('input[name="address"]');
    const phone = form.querySelector('input[name="phone"]');
    
    // Check if cart is empty
    const cart = JSON.parse(localStorage.getItem("cart")) || [];
    if (cart.length === 0) {
        alert('Your cart is empty. Please add items before checkout.');
        return false;
    }
    
    // Validate email
    if (email && !email.value.trim()) {
        alert('Please enter your email address');
        email.focus();
        return false;
    }
    
    // Validate name
    if (name && !name.value.trim()) {
        alert('Please enter your name');
        name.focus();
        return false;
    }
    
    // Validate address
    if (address && !address.value.trim()) {
        alert('Please enter your address');
        address.focus();
        return false;
    }
    
    // Validate phone
    if (phone && !phone.value.trim()) {
        alert('Please enter your phone number');
        phone.focus();
        return false;
    }
    
    return true;
}

// Function to initialize checkout page
function initCheckoutPage() {
    console.log('Initializing checkout page');
    
    // Update cart display
    updateCart();
    
    // Add event listeners for quantity controls in checkout
    const checkoutItems = document.getElementById('checkout_items');
    if (checkoutItems) {
        // Delegate events for quantity controls
        checkoutItems.addEventListener('click', function(e) {
            const target = e.target;
            
            // Handle decrease button
            if (target.classList.contains('decrease_qyantity')) {
                const index = target.getAttribute('data-index');
                decreaseQTY(index);
            }
            
            // Handle increase button
            if (target.classList.contains('increase_qyantity')) {
                const index = target.getAttribute('data-index');
                increaseQTY(index);
            }
            
            // Handle delete button
            if (target.classList.contains('delete_item') || 
                (target.parentElement && target.parentElement.classList.contains('delete_item'))) {
                const button = target.classList.contains('delete_item') ? target : target.parentElement;
                const index = button.getAttribute('data-index');
                removeFromCart(parseInt(index, 10));
            }
        });
    }
    
    // Add coupon button functionality
    const couponButton = document.querySelector('.btn_coupon button');
    if (couponButton) {
        couponButton.addEventListener('click', function(e) {
            e.preventDefault();
            const couponInput = document.querySelector('.coupon input[type="text"]');
            if (couponInput && couponInput.value.trim()) {
                alert('Coupon functionality is not implemented yet.');
            } else {
                alert('Please enter a coupon code.');
            }
        });
    }
}
