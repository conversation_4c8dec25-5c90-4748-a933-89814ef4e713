// Function to create product card HTML
function createProductCard(product) {
    // Ensure product has all required properties with defaults
    const title = product.Name || 'Product Name';
    const description = product.Description || 'No description available';
    const price = product.Price ? parseFloat(product.Price).toFixed(2) : '0.00';
    const oldPrice = product.priceBefore ? parseFloat(product.priceBefore).toFixed(2) : null;
    const discount = oldPrice ? Math.round(((oldPrice - price) / oldPrice) * 100) : 0;
    const image = product.pic1 ? `admin/uploads/items/${product.pic1}` : 'img/default-product.jpg';
    const id = product.item_ID || 0;
    
    // Truncate description if too long
    const shortDescription = description.length > 60 ? 
        description.substring(0, 60) + '...' : 
        description;
    
    // Create HTML for product card
    let html = `
        <div class="swiper-slide">
            <div class="product">
                ${discount > 0 ? `<span class="sale_percent">${discount}%</span>` : ''}
                <div class="img_product">
                    <a href="page-single.php?itemid=${id}">
                        <img src="${image}" alt="${title}">
                    </a>
                </div>
                <div class="product_info">
                    <h3><a href="page-single.php?itemid=${id}">${title}</a></h3>
                    <div class="description">${shortDescription}</div>
                    <div class="price">
                        <span class="current">$${price}</span>
                        ${oldPrice ? `<span class="old">$${oldPrice}</span>` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return html;
}

// Load and initialize products
// fetch('products.json')
// .then(response => response.json())
// .then(data => {

//     const cart = JSON.parse(localStorage.getItem('cart')) || [];
//     // Initialize Swiper for each product category
//     const swiper_items_sale = document.getElementById("swiper_items_sale");
//     const swiper_electronics = document.getElementById("swiper_electronics");
//     const swiper_appliances = document.getElementById("swiper_appliances");
//     const swiper_mobiles = document.getElementById("swiper_mobiles");

//     // Get cart from localStorage
//     // let cart = JSON.parse(localStorage.getItem('cart')) || [];

//     // Helper function to update cart count
//     function updateCartCount() {
//         const cartCount = document.querySelector('.count_item_header');
//         if (cartCount) {
//             cartCount.textContent = cart.length;
//         }
//     }

//     // Helper function to update all instances of a product's button
//     function updateProductButtons(productId, isInCart) {
//         const buttons = document.querySelectorAll(`.btn_add_cart[data-id="${productId}"]`);
//         buttons.forEach(button => {
//             if (isInCart) {
//                 button.classList.add('active');
//                 button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Item in cart';
//             } else {
//                 button.classList.remove('active');
//                 button.innerHTML = '<i class="fa-solid fa-cart-shopping"></i>Add to cart';
//             }
//         });
//     }

//     // Add click event listener for cart buttons
//     document.addEventListener('click', function(e) {
//         const cartBtn = e.target.closest('.btn_add_cart');
//         if (!cartBtn) return;

//         const productId = parseInt(cartBtn.dataset.id);
//         const product = data.find(p => p.id === productId);
        
//         if (product) {
//             const isInCart = cart.some(item => item.id === productId);
            
//             if (isInCart) {
//                 // Remove from cart
//                 cart = cart.filter(item => item.id !== productId);
//                 updateProductButtons(productId, false);
//             } else {
//                 // Add to cart
//                 cart.push(product);
//                 updateProductButtons(productId, true);
//             }

//             // Update localStorage and cart count
//             localStorage.setItem('cart', JSON.stringify(cart));
//             updateCartCount();
//         }
//     });

//     // Populate sections with products
//     data.forEach(product => {
//         if(product.old_price && swiper_items_sale) {



//             swiper_items_sale.innerHTML += createProductHTML(product);
//         }
//         if(product.catetory === "electronics" && swiper_electronics) {
//             swiper_electronics.innerHTML += createProductHTML(product);
//         }
//         if(product.catetory === "appliances" && swiper_appliances) {
//             swiper_appliances.innerHTML += createProductHTML(product);
//         }
//         if(product.catetory === "mobiles" && swiper_mobiles) {
//             swiper_mobiles.innerHTML += createProductHTML(product);
//         }
//     });

//     // Initialize swipers after content is loaded
//     setTimeout(initializeProductSwipers, 100);}).catch(error => {
//     console.error('Error loading products:', error);
// });


// Update cart count on page load
document.addEventListener('DOMContentLoaded', function() {
    const cartCount = document.querySelector('.count_item_header');
    if (cartCount) {
        const cart = JSON.parse(localStorage.getItem('cart')) || [];
        cartCount.textContent = cart.length;
    }
});




