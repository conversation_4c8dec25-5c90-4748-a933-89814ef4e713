<?php

// get all records from any database table 

function getAllfrom($field, $table, $where = NULL, $and = NULL, $orderfield, $ordering = "DESC") {

    global $con;

    $getAll = $con->prepare("SELECT $field FROM $table $where $and ORDER BY $orderfield $ordering");
    
    $getAll->execute();
    
    $all = $getAll->fetchAll();

    return $all;
}


// get categoris function 

// function getCat() {

//     global $con;

//     $getCat = $con->prepare("SELECT * FROM cat ORDER BY ID ASC");
    
//     $getCat->execute();
    
//     $cats = $getCat->fetchAll();

//     return $cats;
// }

// get AD items 


// function getItems($where, $value, $approve = NULL) {

//     global $con;

//     $sql = $approve == NULL ? 'AND Approve = 1' : '';


//     $getItems = $con->prepare("SELECT * FROM items WHERE $where = ? $sql ORDER BY Item_ID DESC");
    
//     $getItems->execute(array($value));
    
//     $items = $getItems->fetchAll();

//     return $items;
// }

// check if users are not activated 

// function to check the regStatus of users 


// Check if user is not activated 
// function to check the RegStatus of the user 

function checkUserStatus($user){
    global $con;

    $stmtx = $con->prepare("SELECT 
                            Username, RegStatus 
                    FROM 
                        users 
                    WHERE 
                        Username = ?
                    AND
                        RegStatus = 0");
$stmtx->execute(array($user));

$status = $stmtx->rowCount();

return $status;

}


// title function that echo the page title 
// in case the page has variable $pageTitle and echo defult title for other pages

function getTitle(){

    global $pageTitle;
    
    if (isset($pageTitle)){

        echo $pageTitle;
    
    } else {
    
        echo 'Default';
    
    }
}



/*
** Check items function v1.0
** function to check item in database [ Function accept parametes]
** $select = the item to select [ example: user, item, category ]
** $from = the table to select from [example: users, items, categories]
**$value = the value of Select 
*/

function checkItem($select, $from, $value) {
    
    global $con;
    
    $statement = $con->prepare("SELECT $select FROM $from WHERE $select = ?");

    $statement->execute(array($value));

    $count = $statement->rowCount();

    return $count;

}


/*
** Home redirect function v2.0
** This function Accept parameters
** $theMsg = Echo the Message [ Error | Success | Warrning ]
** $url = the link you want to redirect to
** $seconds = seconds before redirecting
*/

function redirectHome($theMsg, $url = null, $seconds = 3) {
    
    if($url === null) {

        $url = 'index.php';

        $link = 'Homepage';
    
    } else {

        if(isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '') {

            $url = $_SERVER['HTTP_REFERER'];
        
            $link = 'Previous Page';

        } else {

        $url = 'index.php';

        $link = 'Homepage';
        }
    }
 
            echo $theMsg;
    
            echo "<div class='alert alert-info'>You will be Redirected to $link after $seconds seconds.</div>";
    
            header("refresh:$seconds;url=$url");
    
    exit();
    } 







/* 
** Count number of items function v1.0
** function to count number of items rows
** $item = the items to be count
** $table = the table to choose from
**

*/

function countItems($item, $table) {

    global $con;

    $stmt2 = $con->prepare("SELECT COUNT($item) FROM $table");
    
    $stmt2->execute();
    
    return $stmt2->fetchColumn();
}





function getLatest($select, $table, $order, $limit = 5) {

    global $con;

    $getstmt = $con->prepare("SELECT $select FROM $table ORDER BY $order DESC LIMIT $limit");
    
    $getstmt->execute();
    
    $rows = $getstmt->fetchAll();

    return $rows;
}
