// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
    // Initialize main banner swiper
    var mainSwiper = new Swiper(".slide-swp", {
        pagination: {
            el: ".swiper-pagination",
            dynamicBullets: true,
            clickable: true
        },
        autoplay: {
            delay: 2500,
            disableOnInteraction: false
        },
        loop: true,
        mousewheel: false,
        keyboard: true
    });
    
    // Initialize product image sliders on single product page
    if (document.querySelector('.big-image')) {
        // Count the number of thumbnails
        const thumbnailCount = document.querySelectorAll('.small-image .swiper-slide').length;
        console.log(`Found ${thumbnailCount} thumbnails`);
        
        // Product thumbnail slider - adjust slidesPerView based on available thumbnails
        var productThumb = new Swiper('.small-image', {
            spaceBetween: 10,
            // Show all thumbnails if 5 or fewer, otherwise show 5
            slidesPerView: Math.min(thumbnailCount, 5),
            freeMode: true,
            watchSlidesProgress: true,
            // Disable loop mode for thumbnails
            loop: false,
            breakpoints: {
                320: {
                    slidesPerView: Math.min(thumbnailCount, 3),
                    spaceBetween: 8
                },
                481: {
                    slidesPerView: Math.min(thumbnailCount, 4),
                    spaceBetween: 10
                },
                768: {
                    slidesPerView: Math.min(thumbnailCount, 5),
                    spaceBetween: 15
                }
            }
        });
        
        // Product main image slider
        var productBig = new Swiper('.big-image', {
            spaceBetween: 10,
            // Disable loop mode if there are fewer than 2 slides
            loop: document.querySelectorAll('.big-image .swiper-slide').length > 1,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev'
            },
            thumbs: {
                swiper: productThumb
            }
        });
        
        console.log('Product image sliders initialized with ' + 
                    document.querySelectorAll('.big-image .swiper-slide').length + 
                    ' slides and ' + thumbnailCount + ' thumbnails');
    }
});

// Make sure this code runs after the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all product swipers
    const productSwiperConfig = {
        slidesPerView: 'auto', // Changed from fixed number to 'auto'
        spaceBetween: 20,
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev"
        },
        autoplay: {
            delay: 3000,
            disableOnInteraction: false
        },
        loop: false, // We'll enable it conditionally below
        mousewheel: false,
        keyboard: true,
        centeredSlides: false,
        breakpoints: {
            320: {
                slidesPerView: 1,
                spaceBetween: 10
            },
            480: {
                slidesPerView: 2,
                spaceBetween: 15
            },
            768: {
                slidesPerView: 3,
                spaceBetween: 15
            },
            1024: {
                slidesPerView: 4,
                spaceBetween: 20
            },
            1200: {
                slidesPerView: 5,
                spaceBetween: 20
            }
        }
    };

    // Initialize all slide_product swipers
    const swiperContainers = document.querySelectorAll('.slide_product');
    swiperContainers.forEach(container => {
        const slides = container.querySelectorAll('.swiper-slide');
        const slideCount = slides.length;
        console.log(`Container has ${slideCount} slides`);
        
        // Create a copy of the config for this specific container
        const swiperConfig = {...productSwiperConfig};
        
        // Adjust configuration based on slide count
        if (slideCount <= 1) {
            // If only one slide, disable everything
            swiperConfig.loop = false;
            swiperConfig.autoplay = false;
            swiperConfig.slidesPerView = 1;
            swiperConfig.centeredSlides = true;
        } else if (slideCount < 5) {
            // If fewer than 5 slides, create duplicates to enable loop
            for (let i = 0; i < slides.length; i++) {
                const clone = slides[i].cloneNode(true);
                clone.classList.add('swiper-slide-duplicate');
                container.querySelector('.swiper-wrapper').appendChild(clone);
            }
            swiperConfig.loop = true;
            swiperConfig.loopFillGroupWithBlank = true;
            swiperConfig.loopAdditionalSlides = slideCount;
        } else {
            // If 5 or more slides, enable loop normally
            swiperConfig.loop = true;
        }
        
        // Initialize the swiper with our adjusted config
        new Swiper(container, swiperConfig);
        
        console.log(`Swiper initialized with ${slideCount} slides, loop mode: ${swiperConfig.loop}`);
    });
    
    console.log('Swipers initialized:', swiperContainers.length);
});



