<?php

/*
** Get All function v1.0
** function to get All records from database table
*/


function getAllfrom ($field, $table, $where = NULL, $and = NULL, $orderfield, $ordering = "DESC") {

    global $con;

    $getAll =$con->prepare("SELECT $field FROM $table $where $and ORDER BY $orderfield $ordering");

    $getAll->execute();

    $all = $getAll->fetchAll();

    return $all;

}


// title function that echo the page title 
// in case the page has variable $pageTitle and echo defult title for other pages

function getTitle(){

    global $pageTitle;
    
    if (isset($pageTitle)){

        echo $pageTitle;
    
    } else {
    
        echo 'Default';
    
    }
}


/*
** Home redirect function v2.0
** This function Accept parameters
** $theMsg = Echo the Message [ Error | Success | Warrning ]
** $url = the link you want to redirect to
** $seconds = seconds before redirecting
*/

function redirectHome($theMsg, $url = null, $seconds = 3) {
    
    if($url === null) {

        $url = 'index.php';

        $link = 'Homepage';
    
    } else {

        if(isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] !== '') {

            $url = $_SERVER['HTTP_REFERER'];
        
            $link = 'Previous Page';

        } else {

        $url = 'index.php';

        $link = 'Homepage';
        }
    }
 
            echo $theMsg;
    
            echo "<div class='alert alert-info'>You will be Redirected to $link after $seconds seconds.</div>";
    
            header("refresh:$seconds;url=$url");
    
    exit();
    } 



/*
** Check items function v1.0
** function to check item in database [ Function accept parametes]
** $select = the item to select [ example: user, item, category ]
** $from = the table to select from [example: users, items, categories]
**$value = the value of Select 
*/

function checkItem($select, $from, $value) {
    
    global $con;
    
    $statement = $con->prepare("SELECT $select FROM $from WHERE $select = ?");

    $statement->execute(array($value));

    $count = $statement->rowCount();

    return $count;

}



/* 
** Count number of items function v1.0
** function to count number of items rows
** $item = the items to be count
** $table = the table to choose from
**

*/

function countItems($item, $table) {

    global $con;

    $stmt2 = $con->prepare("SELECT COUNT($item) FROM $table");
    
    $stmt2->execute();
    
    return $stmt2->fetchColumn();
}





function getLatest($select, $table, $order, $limit = 5) {

    global $con;

    $getstmt = $con->prepare("SELECT $select FROM $table ORDER BY $order DESC LIMIT $limit");
    
    $getstmt->execute();
    
    $rows = $getstmt->fetchAll();

    return $rows;
}



// Define helper functions
function generateCategoryOptions($selectedId = 0) {
    global $con; // Ensure database connection is available
    $options = '<option value="0">...</option>';
    $allCats = getAllfrom("*", "cat", "where parent = 0", "", "ID");
    
    foreach($allCats as $cat) {
        $selected = ($selectedId == $cat['ID']) ? 'selected' : '';
        $options .= "<option value='{$cat['ID']}' {$selected}>{$cat['Name']}</option>";
        
        $childCats = getAllfrom("*", "cat", "where parent = {$cat['ID']}", "", "ID");
        foreach($childCats as $child) {
            $selected = ($selectedId == $child['ID']) ? 'selected' : '';
            $options .= "<option value='{$child['ID']}' {$selected}> ===> {$child['Name']}</option>";
        }
    }
    
    return $options;
}

// not working
function processImageUpload($fileInput, $existingImage = '') {
    // Check if the directory exists and is writable
    if(!is_dir(UPLOAD_PATH)) {
        mkdir(UPLOAD_PATH, 0755, true);
    }
    
    if(!is_writable(UPLOAD_PATH)) {
        // Log error or handle appropriately
        return $existingImage;
    }
    
    // Check if file was uploaded
    if(!isset($_FILES[$fileInput]) || $_FILES[$fileInput]['error'] == UPLOAD_ERR_NO_FILE) {
        return $existingImage; // Keep existing image if no new one uploaded
    }
    
    $file = $_FILES[$fileInput];
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    
    // If no file uploaded, return existing
    if(empty($fileName)) {
        return $existingImage;
    }
    
    // Validate file
    $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    if(!in_array($extension, ALLOWED_EXTENSIONS)) {
        return false; // Invalid extension
    }
    
    if($fileSize > MAX_FILE_SIZE) {
        return false; // File too large
    }
    
    // Generate unique filename and upload
    $newFileName = rand(0, 100000000) . '_' . $fileName;
    
    // Upload file
    if(move_uploaded_file($fileTmp, UPLOAD_PATH . $newFileName)) {
        return $newFileName;
    } else {
        return false; // Upload failed
    }
}

function displayItemImage($imagePath) {
    if(empty($imagePath)) {
        return 'No Image';
    } else {
        return "<a data-fslightbox href='uploads/items/{$imagePath}'><img src='uploads/items/{$imagePath}' alt=''/></a>";
    }
}
