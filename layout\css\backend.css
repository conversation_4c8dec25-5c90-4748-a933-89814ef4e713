
/* Start Main rulez */

body {
    background-color: #dbd8d8;
    font-size: 16px;

}


.asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #8f2018;
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:focus,
.navbar-inverse .navbar-nav>.open>a:hover,
.dropdown-menu {
    background-color: #3498db;
}


.dropdown-menu {
    min-width: 180px;
    padding: 0;
    font-size: 1em;
    border: none;
    border-radius: 0;
}

.dropdown-menu>li>a {
    color: #fff;
    padding: 10px 15px;
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
    color: #fff;
    background-color: #8e44ad;
}

.nice-message {
    padding: 10px;
    background-color: #FFF;
    margin: 10px 0;
    border-left: 5px solid #080;
}

/* End Main Rulez */


/* start login form */

.login {
    width: 300px;
    margin: 100px auto;
}

.login h4 {

    color: #888;
}

.login input {
    margin-bottom: 10px;
}

.login .form-control {
    background-color: #EAEAEA;
}

.login .btn {
    background-color: blueviolet;
}

.navbar {
    border-radius: 0;
    margin-bottom: 0;
}

.nav>li>a,
.navbar-brand {
    font-size: 1em;
}


.form-control {
    position: relative;

}

/*End bootstrap edits */

/* Start Dashboard page */

.home-stats .stat {
    padding: 20px;
    font-size: 15px;
    color: #FFF;
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.home-stats .stat i {
    position: absolute;
    font-size: 80px;
    top: 35px;
    left: 30px;
}

.home-stats .stat .info {
    float: right;
}

.home-stats .stat a {
    color: #FFF;
}

.home-stats .stat a:hover {
    text-decoration: none;
}

.home-stats .stat span {
    display: block;
    font-size: 60px;
}

.home-stats .st-members {
    background-color: #3498db;


}

.home-stats .st-pending {
    background-color: #e67e22;



}

.home-stats .st-items {
    background-color: #16a085;

}

.home-stats .st-comments {
    background-color: #8e44ad;

}


.latest {
    margin-top: 30px;
}

.latest .toggle-info {
    color: #999;
    cursor: pointer;

}

.latest .toggle-info:hover {
    color: #444;
}

.latest-users {
    margin-bottom: 0;
}

.latest-users li {
    padding: 10px;
    overflow: hidden;
}

.latest-users li:nth-child(odd) {
    background-color: #EEE;

}

.latest-users .btn-success .latest-users .btn-info {
    padding: 2px 8px;

}

.latest-users .btn-info {
    margin-right: 5px;

}

.latest .comment-box {
    margin: 5px 0 10px;

}

.latest .comment-box .member-n,
.latest .comment-box .member-c {
    float: left;
}

.latest .comment-box .member-n {
    width: 80px;
    text-align: center;
    margin-right: 20px;
    position: relative;
    top: 10px;

}

.latest .comment-box .member-c {
    width: calc(100% - 100px);
    background-color: #EFEFEF;
    padding: 10px;
    position: relative;
}

.latest .comment-box .member-c:before {
    content: "";
    display: block;
    position: absolute;
    left: -28px;
    top: 5px;
    width: 0;
    height: 0;
    border-style: solid;
    border-color: transparent #EFEFEF transparent transparent;
    border-width: 15px;


}



/* End of Dashboard page */

/* Start Member page */

h1 {
    font-size: 55px;
    margin: 40px 0;
    font-family: bold;
    color: #666;

}

.show-pass {
    position: absolute;
    top: 6px;
    right: -30px;
}

.manage-members img {
    width: 50px;
    height: 50px;

}

.main-table {
    -webkit-box-shodow: 0 3px 10px #CCC;
    -moz-box-shadow: 0 3px 10px #CCC;
    box-shadow: 0 3px 10px #CCC;
}

.main-table td {
    background-color: #FFF;
    vertical-align: middle !important;


}

.main-table tr:first-child td {
    background-color: #333;
    color: #FFF;

}

.main-table .btn {
    padding: 3px 10px;
}

.activate {
    margin-left: 5px;
}

/* End Member page */
/* Start Categories page */

.categories .panel-heading {
    color: #050505;
    font-weight: bold;
}

.categories .panel-heading i {
    position: relative;
    top: 1px;
}

.categories .panel-body {
    padding: 0;
}

.categories .option a {
    color: #888;
    text-decoration: none;
}


.categories .option span {
    color: #888;
    cursor: pointer;
}

.categories .option .active {
    color: #f00;
    font-weight: bold;

}

.categories hr {
    margin-top: 0;
    margin-bottom: 0;
}

.categories .cat {
    padding: 15px;
    position: relative;
    overflow: hidden;
}

.categories .cat:hover {
    background-color: #EEE;
}

.categories .cat:hover .hidden-buttons {
    right: 10px;
}

.categories .cat .hidden-buttons {

    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;
    position: absolute;
    top: 20px;
    right: -120px;
}



.categories .cat .hidden-buttons a {
    margin-right: 5px;
}

.categories .cat h3 {
    margin: 0;
    cursor: pointer;
    font-weight: bold;
    color: #6A6A6A;
}


.categories .cat .full-view p {
    margin: 10px 0;
    color: #707070;
    
}

.categories .cat:last-of-type ~ hr {
    display: none;
}


.categories .cat .visibility {
    background-color: #d35400;
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px;
}


.categories .cat .commenting {
    background-color: #2c3e50;
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px;
}

.categories .cat .advertises {
    background-color: #c0392b;
    color: #FFF;
    padding: 4px 6px;
    margin-right: 6px;
    border-radius: 6px;
}

.categories .add-category {
    margin-top: -10px;
    margin-bottom: 30px;
}

.categories .child-head {
    margin: 15px 0 10px;
    font-weight: bold;
    font-size: 16px;
    color: #22ab79;
}

.categories .child-cats {
    margin: 0;
}

.categories .child-cats li {
    margin-left: 15px;
}

.categories .child-cats li:before {
    content: "~";

}

.categories .show-delete {
    color: #f00;
    display: none;

}

/* End  Categories page */

/* Category management styling */
.cat .badge {
    background-color: #5cb85c;
    font-size: 12px;
    vertical-align: middle;
}

.cat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.child-head {
    margin-top: 15px;
    margin-bottom: 5px;
    color: #666;
    font-weight: bold;
}

.sub-cats {
    margin-left: 20px;
    border-left: 1px solid #eee;
    padding-left: 10px;
}

.no-items {
    color: #999;
    font-style: italic;
    margin-top: 10px;
}

