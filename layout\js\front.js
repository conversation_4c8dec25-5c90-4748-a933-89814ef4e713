
    // Hide Placeholder On Form Focus

    $('[placeholder]').focus(function () {

      $(this).attr('data-text', $(this).attr('placeholder'));

      $(this).attr('placeholder', '');

  }).blur(function () {

      $(this).attr('placeholder', $(this).attr('data-text'));

  });

      // Add Asterisk on required fiels

      $('input').each(function () {

        if ($(this).attr('required') === 'required') {

            $(this).after('<span class="asterisk">*</span>');

        }
    });



        // switch between login and signup pages 

      $('.login-page h1 span').click(function() {
      $(this).addClass('selected').siblings().removeClass('selected');
      $('.login-page form').hide();
      $('.' + $(this).data('class')).fadeIn(100);

    });


        // Convert password field to text field on hover

        var passField = $('.password');


        $('.show-pass').hover(function () {
    
            passField.attr('type', 'text');
    
        }, function () {
            passField.attr('type', 'password');
    
        });
    
        // Confirmation message on button
    
        $('.confirm').click(function () {
    
            return confirm('Are you sure you want to delete this member ?');
    
        });
    
    



  


// copy menu for mobile 

function copyMenu(){
    // copy inside .dpt-cat to .departments 
    var dptCategory = document.querySelector('.dpt-cat');
    var dptPlace = document.querySelector('.departments');
    dptPlace.innerHTML = dptCategory.innerHTML;
  
    // copy insde nav to nav 
    var mainNav = document.querySelector('.header-nav nav');
    var navPlace = document.querySelector('.off-canvas nav')
    navPlace.innerHTML = mainNav.innerHTML;
  
   
  }
  copyMenu();

//   show mobile menu 

const menuButton = document.querySelector('.trigger'),
  closeButton = document.querySelector('.t-close'),
  addclass = document.querySelector('.site');

  menuButton.addEventListener('click',function() {
    addclass.classList.toggle('showmenu')
  })
  closeButton.addEventListener('click', function(){
    addclass.classList.remove('showmenu')
  })

//   show sub menu on mobile 
const submenu = document.querySelectorAll('.has-child .icon-small');
submenu.forEach((menu) => menu.addEventListener('click', toggle));

function toggle(e) {
    e.preventDefault();
    submenu.forEach((item) => item != this ? item.closest('.has-child').classList.remove('expand') : null);

    if (this.closest('.has-child').classList != 'expand');
    this.closest('.has-child').classList.toggle('expand')
}

// slider 

const swiper = new Swiper('.swiper', {
  loop: true,

  // If we need pagination
  pagination: {
    el: '.swiper-pagination',
  },

  // Navigation arrows
  // navigation: {
  //   nextEl: '.swiper-button-next',
  //   prevEl: '.swiper-button-prev',
  // },

  // And if we need scrollbar
  // scrollbar: {
  //   el: '.swiper-scrollbar',
  // },







});

// show search 

const searchButton = document.querySelector('.t-search'),
  tClose = document.querySelector('.search-close'),
  showClass = document.querySelector('.site');

  searchButton.addEventListener('click', function(){
    showClass.classList.toggle('showsearch')
  })
  tClose.addEventListener('click', function (){
    showClass.classList.remove('showsearch')
  })


  const dptButton = document.querySelector('.dpt-cat .dpt-trigger'),
        dptClass = document.querySelector('.site');
  dptButton.addEventListener('click', function(){
    dptClass.classList.toggle('showdpt')
  })


  // product image slider

  var productThumb = new Swiper ('.small-image', {
    loop: true,
    spaceBetween: 10,
    slidesPerView: 3,
    freeMode: true,
    watchSlidesProgress: true,
    breakpoints: {
      481: {
        spaceBetween: 32,
      }
    }
  });
  var productBig = new Swiper ('.big-image', {
    loop: true,
    autoHeight: true,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev'
    },
    thumbs: {
      swiper: productThumb
    }
  })

  // stock products bar width percentage 

  var stocks = document.querySelectorAll('.products .stock');
  for (let x = 0; x < stocks.length; x++) {
    let stock = stocks[x].dataset.stock,
    available = stocks[x].querySelector('.qty-available').innerHTML,
    sold = stocks[x].querySelector('.qty-sold').innerHTML,
    percent = sold*100/stock;

    stocks[x].querySelector('.available').style.width = percent + '%'
  }

  // show cart on click

  const divtoShow = '.mini-cart';
  const divPopup = document.querySelector(divtoShow);
  const divTrigger = document.querySelector('.cart-trigger');

  divTrigger.addEventListener('click', () => {
    setTimeout(() => {
      if(!divPopup.classList.contains('show')) {
        divPopup.classList.add('show');
      }
    } , 250)
  })

  // close by click outside

  document.addEventListener('click', (e) => {
    const isClosest = e.target.closest(divtoShow);
    if(!isClosest && divPopup.classList.contains('show')) {
      divPopup.classList.remove('show')
    }
  })


  


  // show modal on load 
  // window.onload = function () {
  //   document.querySelector('.site').classList.toggle('showmodal')
  // }
  // document.querySelector('.modalclose').addEventListener('click', function () {
  //   document.querySelector('.site').classList.remove('showmodal')
  // })


  