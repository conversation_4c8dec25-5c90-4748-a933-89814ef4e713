<?php
// Add at the top of your file
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);



// Items page 
ob_start();
session_start();
$pageTitle = 'Items';

// Define constants
define('MAX_FILE_SIZE', 2097152); // 2MB
define('ALLOWED_EXTENSIONS', ["jpeg", "jpg", "png", "gif", "webp"]);
define('UPLOAD_PATH', "uploads/items/");


if (isset($_SESSION['Username'])) {
    include 'init.php';
    $do = isset($_GET['do']) ? $_GET['do'] : 'Manage';
        
        if ($do == 'Manage') {

            $stmt = $con->prepare("SELECT 
                        items.*, 
                        cat.Name AS cat_name,
                        cat.parent,
                        parent_cat.Name AS parent_cat_name,
                        users.Username,
                        sections.Name AS section_name
                    FROM 
                        items
                    INNER JOIN 
                        cat 
                    ON 
                        cat.ID = items.Cat_ID
                    LEFT JOIN
                        cat AS parent_cat
                    ON
                        parent_cat.ID = cat.parent
                    INNER JOIN 
                        users 
                    ON 
                        users.UserID = items.Member_ID
                    LEFT JOIN
                        cat AS sections
                    ON
                        sections.ID = items.sectionID
                    ORDER BY
                        Item_ID DESC
                        ");

            // Execute the statement
            $stmt->execute();

            // Assign to variable
            $items = $stmt->fetchall();

                if (! empty($items)) {

                    
                ?>
                <a href="items.php?do=Add" class="btn btn-sm btn-primary"><i class="fa fa-plus"></i>Add new Item</a>

                <h1 class="text-center">Manage Items</h1>
                <div class="container"> 
                    <div class="table-responsive">
                        <table class="main-table manage-items text-center table table-bordered">
                            <tr>
                                <td>#ID</td>
                                <td>Name</td>
                                <td>Pictures</td>
                                <td>Description</td>
                                <td>Price</td>
                                <td>Section</td>
                                <td>Category</td>
                                <td>Sub-Category</td>

                                <td>Adding Date</td>
                                <td>Username</td>
                                <td>Control</td>
                            </tr>
                        
                        <?php
                            foreach($items as $item) {

                            echo "<tr>";

                                echo "<td>" . $item['Item_ID'] . "</td>";

                                echo "<td>" . $item['Name'] . "</td>";

                                echo "<td>";

                                if(empty($item['pic1'])) {
                                    
                                    echo 'No Image';
                                    }else{
                                        echo "<a data-fslightbox href='" . UPLOAD_PATH . $item['pic1'] . "'><img src='" . UPLOAD_PATH . $item['pic1'] . "' alt=''/></a>";
                                    }


                                if(empty($item['pic2'])) {

                                    echo 'No Image';
                                    }else{
                                        echo "<a data-fslightbox href='" . UPLOAD_PATH . $item['pic2'] . "'><img src='" . UPLOAD_PATH . $item['pic2'] . "' alt=''/></a>";
                                    }


                                if(empty($item['pic3'])) {
                                    echo 'No Image';
                                    }else{
                                        echo "<a data-fslightbox href='" . UPLOAD_PATH . $item['pic3'] . "'><img src='" . UPLOAD_PATH . $item['pic3'] . "' alt=''/></a>";
                                    }



                                if(empty($item['pic4'])) {
                                    echo 'No Image';
                                    }else{
                                        echo "<a data-fslightbox href='" . UPLOAD_PATH . $item['pic4'] . "'><img src='" . UPLOAD_PATH . $item['pic4'] . "' alt=''/></a>";
                                    } 
                                
                                
                                
                                if(empty($item['pic5'])) {

                                    echo 'No Image';
                                    }else{
                                        echo "<a data-fslightbox href='" . UPLOAD_PATH . $item['pic5'] . "'><img src='" . UPLOAD_PATH . $item['pic5'] . "' alt=''/></a>";
                                    }
                                
                                    
                                
                                echo "</td>";

                                
                                
                                echo "<td>" . $item['Description'] . "</td>";
                                
                                echo "<td>" . $item['Price'] . "</td>";
                                
                                echo "<td>";
                                if ($item['sectionID'] == 0) {
                                    echo "None";
                                } elseif ($item['sectionID'] == 1) {
                                    echo "Section";
                                } else {
                                    echo $item['section_name'] ? $item['section_name'] : "Unknown Section";
                                }
                                echo "</td>";

                                
                                                                
                                echo "<td>";
                                if (!empty($item['Cat_ID'])) {
                                    // The cat_name is already fetched in the JOIN
                                    echo $item['cat_name'];
                                } else {
                                    echo "None";
                                }
                                echo "</td>";



                                                                
                                // echo "<td>" . $item['cat_name'] . "</td>";

                                echo "<td>";
                                if (!empty($item['subcat'])) {
                                    // Get the category name for this subcategory ID
                                    $subcatInfo = getAllfrom("Name", "cat", "where ID = " . $item['subcat'], "", "ID", "ASC");
                                    if (!empty($subcatInfo)) {
                                        echo $subcatInfo[0]['Name'];
                                    } else {
                                        echo $item['subcat'] . " (Unknown)";
                                    }
                                } else {
                                    echo "None";
                                }
                                echo "</td>";

                                echo "<td>" . $item['Add_Date'] . "</td>";




                                

                                echo "<td>" . $item['Username'] . "</td>";
                                
                                    echo "<td>
                                        <a href='items.php?do=Edit&itemid=" . $item['Item_ID'] . "' class='btn btn-success'><i class='fa fa-edit'></i>Edit</a>
                                        <a href='items.php?do=Delete&itemid=" . $item['Item_ID'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i>Delete</a>";
                        
                                        if ($item['Approve'] == 0) {
                                            echo "<a href='items.php?do=Approve&itemid=" . $item['Item_ID'] . "'class='btn btn-info activate'><i class='fa fa-check'></i> Approve</a>"; 
                        
                                            }else{
                                                
                                            if ($item['Approve'] == 1) {
                            
                                                echo "<a href='items.php?do=Hide&itemid=" . $item['Item_ID'] . "'class='btn btn-info activate'><i class='fa fa-check'></i> Hide</a>"; 
                            
                                            }
                                        }



                                echo "</td>";
                            echo "</tr>";
                            
                            }
                                
                        ?>
                        
                        <!-- <tr>  -->
                        <!-- what is this tag for ?   -->
                    </table>
                    
                </div>

                <a href="items.php?do=Add" class="btn btn-sm btn-primary"><i class="fa fa-plus"></i>Add new Item</a>

                <?php 
            } else {
            
                    echo '<div class="container">';
                    
                    echo '<div class="nice-message">There is No Items to show </div>';
                    
                    echo '<a href="items.php?do=Add" class="btn btn-sm btn-primary">
                    <i class="fa fa-plus"></i>Add new Item</a>';

                    echo '</div>';
            
            }
            
        } elseif ($do == 'Add') {
        
                ?>

            <!-- <h5 class="text-left"> Username : <?php echo $_SESSION['Username']?> </h5> -->
            <h1 class="text-center"> Add New Item </h1>

            <div class="container ">
            

                <form action="?do=Insert" method="POST" enctype="multipart/form-data">

                    <div class="row">

                        <div class="dataInpunt">

                        
                            <!-- start name field  -->
                            <div class="form-group">
                                <label> Name </label>
                                <div class="inputname">
                                    <input type="text" 
                                    name="name" 
                                    class="form-control" 
                                    required="required" 
                                    placeholder=" Name of item"/>
                                </div>
                            </div>
                            <!-- End name field  -->

                                    
                            <!-- start Brand field  -->
                            <div class="form-group">
                                <label> Brand </label>
                                <div class="inputname">
                                    <input type="text"
                                    name="brand" 
                                    class="form-control" 
                                    required="required" 
                                    placeholder=" brand of item"/>
                                </div>
                            </div>
                            <!-- End Brand field  -->


                            <!-- start description field  -->
                            <div class="form-group">
                                <label> Description </label>
                                <div class="inputname">
                                    <input type="text" 
                                    name="description" 
                                    class="form-control" 
                                    placeholder=" description of item"
                                    required="required"/>
                                </div>
                            </div>
                            <!-- End description field  -->

                            
                            <!-- start Tags field  -->
                            <div class="form-group">
                                <label> Tags </label>
                                <div class="inputname">
                                    <input type="text" 
                                    name="tags" 
                                    class="form-control" 
                                    placeholder=" Tags describe your ads, separate tags with comma , "
                                    required="required"/>
                                </div>
                            </div>
                            <!-- End Tags field  -->


                                <div class="indentity">
                    
                                    <!-- start Section field  -->
                                    <div class="form-group">
                                        
                                        <div class="inputname">
                                        <label> Section </label>
                                            <select class="selected" name="section" id="section-select">
                                                <option value="0">...</option>
                                                <?php
                                                // Get all sections (categories with section = 1)
                                                $allSections = getAllfrom("*", "cat", "where section = 1", "", "ID", "ASC");
                                                
                                                foreach($allSections as $section) {
                                                    echo "<option value='" . $section['ID'] . "'>" . $section['Name'] . "</option>";
                                                }
                                                ?>
                                            </select>
                                    
                                        </div>
                                    </div>
                                    <!-- End Section field  -->
                                    

                                    
                                    <!-- start Categories field  -->
                                    <div class="form-group">
                                        
                                        <div class="inputname">
                                            <label> Category </label>
                                            <select class="selected" name="category">
                                                <option value="0">...</option>
                                                <?php
                                                $allCats = getAllfrom("*", "cat", "where parent = 0", "", "ID");
                                                
                                                foreach($allCats as $cat) {
                                                    echo "<option value='" . $cat['ID'] . "'>" . $cat['Name'] . "</option>";
                                                    $childCats = getAllfrom("*", "cat", "where parent = {$cat['ID']}", "", "ID");
                                                    
                                                    
                                                    // foreach($childCats as $child){
                                                    // echo "<option value='" . $child['ID'] . "'> ===> " . $child['Name'] . "</option>";

                                                    // }
                                                    }
                                                    ?>

                                            </select>
                                    
                                        </div>
                                    </div>
                                    <!-- End Categories field  -->
                                    

                                    <!-- start Sub Categories field  -->
                                    <div class="form-group">
    
                                        <div class="inputname">
                                            <label>Sub-Category </label>
                                            <select class="selected" name="subcategory">
                                                <option value="0">...</option>
                                                <?php
                                                $allCats = getAllfrom("*", "cat", "where parent != 0", "", "ID");
                                                
                                                foreach($allCats as $cat) {
                                                    echo "<option value='" . $cat['ID'] . "'>" . $cat['Name'] . "</option>";
                                                    $childCats = getAllfrom("*", "cat", "where parent = {$cat['ID']}", "", "ID");

                                                    // foreach($childCats as $child){
                                                    // echo "<option value='" . $child['ID'] . "'> ===> " . $child['Name'] . "</option>";

                                                    // }
                                                    }
                                                    ?>

                                            </select>
                                    
                                        </div>
                                    </div>
                                    <!-- End Categories field  -->
                                    


                                </div>            
                            
                                <div class="pricearea">

                                    <!-- start price field  -->
                                    <div class="form-group">
                                        <label> Price </label>
                                        <div class="inputname">
                                            <input type="text" 
                                            name="price" 
                                            class="form-control" 
                                            placeholder=" item price"/>
                                        </div>
                                    </div>
                                    <!-- End price field  -->
                                    
                                    
                                    <!-- start pricebefore field  -->
                                    <div class="form-group">
                                        <label>Price befor disc%</label>
                                        <div class="inputname">
                                            <input type="text" 
                                            name="pricebefore" 
                                            class="form-control" 
                                            placeholder="Price before discount"/>
                                        </div>
                                    </div>
                                    <!-- End pricebefore field  -->
                                    

                                    <!-- start Discount field  -->
                                    <div class="form-group">
                                        <label> Discount </label>
                                        <div class="inputname">
                                            <input type="text" 
                                            name="discount" 
                                            class="form-control" 
                                            placeholder=" item discount"/>
                                        </div>
                                    </div>
                                    <!-- End Discount field  -->



                                </div>


                                <div class="conditionarea">
                                                
                                    <!-- start Hot deals field  -->
                                    <div class="form-group">
                                        <div class="custom-select">


                                        <label> Hot deal </label>

                                            <select class="selected" name="hotdeals">
                                                <option value="0">No </option>
                                                <option value="1">Hot Deal</option>

                                            </select>
                                    
                                        </div>
                                    </div>
                                    <!-- End Hot seals field   -->

                                    
                                    
                                    <!-- start Trend field  -->
                                    <div class="form-group">
                                        <div class="custom-select">

                                        <label> Trend </label>

                                            <select class="selected" name="trend">
                                                <option value="0">No </option>
                                                <option value="1"> Trend </option>

                                            </select>
                                    
                                        </div>
                                    </div>
                                    <!-- End Trend field   -->


                                    <!-- start rating field  -->
                                    <div class="form-group">
                                        <div class="custom-select">
                                        
                                        <label> Rating </label>


                                            <select name="rating" id="">
                                                <option value="0">  ---  </option>
                                                
                                                <option value="1">*</option>
                                                <option value="2">**</option>
                                                <option value="3">***</option>
                                                <option value="4">****</option>
                                                <option value="5">*****</option>
                                            </select>

                                        
                                        </div>
                                    </div>
                                    <!-- End rating field  -->



                                </div>


                        </div>

                        <div class="picInput">

                            <!-- start Items pictures field  -------Image 1-----------          -->
                            <div class="hero">
                                <label for="inputpic"> Upload image 1 </label>
                                <img src="uploads/userdefault.jpg" alt="" id="prod-pic">
                                <div class="prodcard">
                                    <input type="file" name="pic1" class="form-control" required="required" id="inputpic"/>
                                </div>
                            </div>
                            <!-- End Items pictures field  -------Image 1-----------            -->        
                        

                            <!-- start Items pictures field  -------Image 2-----------          -->
                            <div class="hero">
                                <label for="inputpicB"> Upload image 2 </label>
                                <img src="uploads/userdefault.jpg" alt="" id="prodpicB">
                                <div class="prodcard">
                                    <input type="file" name="pic2" class="form-control" id="inputpicB"/>
                                </div>
                            </div>
                            <!-- End Items pictures field  -------Image 2-----------            -->        
                        

                            <!-- start Items pictures field  -------Image 3-----------          -->
                            <div class="hero">
                                <label for="inputpicC"> Upload image 3 </label>
                                <img src="uploads/userdefault.jpg" alt="" id="prodpicC">
                                <div class="prodcard">
                                    <input type="file" name="pic3" class="form-control" id="inputpicC"/>
                                </div>
                            </div>
                            <!-- End Items pictures field  -------Image 3-----------            -->        
                        

                            <!-- start Items pictures field  -------Image 4-----------          -->
                            <div class="hero">
                                <label for="inputpicD"> Upload image 4 </label>
                                <img src="uploads/userdefault.jpg" alt="" id="prodpicD">
                                <div class="prodcard">
                                    <input type="file" name="pic4" class="form-control" id="inputpicD"/>
                                </div>
                            </div>
                            <!-- End Items pictures field  -------Image 4-----------            -->        
                            

                            <!-- start Items pictures field  -------Image 5-----------          -->
                            <div class="hero">
                                <label for="inputpicE"> Upload image 5 </label>
                                <img src="uploads/userdefault.jpg" alt="" id="prodpicE">
                                <div class="prodcard">
                                    <input type="file" name="pic5" class="form-control" id="inputpicE"/>
                                </div>
                            </div>
                            <!-- End Items pictures field  -------Image 5-----------            -->        


                        </div>
                    </div>

                    <!-- start Submit/Save btn  -->
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                            <input type="submit" value="Add item" class="btn btn-primary btn-sm" />
                        </div>
                    </div>
                    <!-- End Submit/Save btn  -->

                </form>
            </div>

            <?php

        } elseif ($do == 'Insert') {
        
            if ($_SERVER['REQUEST_METHOD'] == 'POST') {

                echo "<h1 class='text-center'> Insert Item </h1>";
                echo "<div class='container'>";

                // Uploade items picture variables

                // image 1

                $pic1 = $_FILES['pic1'];
                $pic1Name = $_FILES['pic1']['name'];
                $pic1Size = $_FILES['pic1']['size'];
                $pic1Tmp = $_FILES['pic1']['tmp_name'];
                $pic1Type = $_FILES['pic1']['type'];
                $pic1AllowedExtension = array("jpeg", "jpg", "png", "gif");
                $pic1Extension = explode('.', $pic1Name);
                $dump1 = strtolower(end($pic1Extension));

                // image 2
                $pic2 = $_FILES['pic2'];
                $pic2Name = $_FILES['pic2']['name'];
                $pic2Size = $_FILES['pic2']['size'];
                $pic2Tmp = $_FILES['pic2']['tmp_name'];
                $pic2Type = $_FILES['pic2']['type'];
                $pic2AllowedExtension = array("jpeg", "jpg", "png", "gif");
                $pic2Extension = explode('.', $pic2Name);
                $dump2 = strtolower(end($pic2Extension));

                // image 3
                $pic3 = $_FILES['pic3'];
                $pic3Name = $_FILES['pic3']['name'];
                $pic3Size = $_FILES['pic3']['size'];
                $pic3Tmp = $_FILES['pic3']['tmp_name'];
                $pic3Type = $_FILES['pic3']['type'];
                $pic3AllowedExtension = array("jpeg", "jpg", "png", "gif");
                $pic3Extension = explode('.', $pic3Name);
                $dump3 = strtolower(end($pic3Extension));

                // image 4
                $pic4 = $_FILES['pic4'];
                $pic4Name = $_FILES['pic4']['name'];
                $pic4Size = $_FILES['pic4']['size'];
                $pic4Tmp = $_FILES['pic4']['tmp_name'];
                $pic4Type = $_FILES['pic4']['type'];
                $pic4AllowedExtension = array("jpeg", "jpg", "png", "gif");
                $pic4Extension = explode('.', $pic4Name);
                $dump4 = strtolower(end($pic4Extension));

                // image 5
                $pic5 = $_FILES['pic5'];
                $pic5Name = $_FILES['pic5']['name'];
                $pic5Size = $_FILES['pic5']['size'];
                $pic5Tmp = $_FILES['pic5']['tmp_name'];
                $pic5Type = $_FILES['pic5']['type'];
                $pic5AllowedExtension = array("jpeg", "jpg", "png", "gif");
                $pic5Extension = explode('.', $pic5Name);
                $dump5 = strtolower(end($pic5Extension));

                // get variables from from 

                $name           = $_POST['name'];
                $brand          = $_POST['brand'];
                $desc           = $_POST['description'];
                $tags           = $_POST['tags'];
                $section        = $_POST['section'];
                $cat            = $_POST['category'];
                $subcat         = $_POST['subcategory'];
                $price          = $_POST['price'];
                $pricebefore    = $_POST['pricebefore'];
                $discount       = $_POST['discount'];
                $hotdeal        = $_POST['hotdeals'];
                $trend          = $_POST['trend'];
                $rating         = $_POST['rating'];
                $member         = $_SESSION['ID'];

                // validate the form 

                $formErrors = array();

                if(empty($name)) {
                    $formErrors[] = 'name can not be<strong> empty !</strong>';

                }

                if(empty($brand)) {
                    $formErrors[] = 'name can not be<strong> empty !</strong>';

                }

                if(empty($desc)) {
                    $formErrors[] = 'description can not be<strong> empty !</strong>';

                }

                if (empty($price)) {
                    $formErrors[] = 'price can not be <strong> empty !</strong>';
                }

                if ($section == 0) {
                    $formErrors[] = 'you must choose the <strong> Section !</strong>';
                }

                if ($cat == 0) {
                    $formErrors[] = 'you must choose the <strong> category !</strong>';
                }
                // check if file is empty

                //image_1
                if(! empty($pic1Name) && ! in_array($dump1, ALLOWED_EXTENSIONS)) {
                    $formErrors[] = 'Image#_1 extension is not allowed <strong> !</strong>';
                }

                if( empty($pic1Name)) {
                    $formErrors[] = 'Image#_1 can not be<strong> empty !</strong>';
                }

                if ($pic1Size > MAX_FILE_SIZE) {
                    $formErrors[] = 'Image#_1 file is too large (max ' . (MAX_FILE_SIZE/1048576) . 'MB)';
                }

                //image_2
                if(! empty($pic2Name) && ! in_array($dump2, ALLOWED_EXTENSIONS)) {
                    $formErrors[] = 'Image#_2 extension is not allowed <strong> !</strong>';
                }

                if( empty($pic2Name)) {
                    $formErrors[] = 'Image#_2 can not be<strong> empty !</strong>';
                }

                if ($pic2Size > MAX_FILE_SIZE) {
                    $formErrors[] = 'Image#_2 file is too large (max ' . (MAX_FILE_SIZE/1048576) . 'MB)';
                }

                //image_3
                if(! empty($pic3Name) && ! in_array($dump3, ALLOWED_EXTENSIONS)) {
                    $formErrors[] = 'Image#_3 extension is not allowed <strong> !</strong>';
                }

                if( empty($pic3Name)) {
                    $formErrors[] = 'Image#_3 can not be<strong> empty !</strong>';
                }

                if ($pic3Size > MAX_FILE_SIZE) {
                    $formErrors[] = 'Image#_3 file is too large (max ' . (MAX_FILE_SIZE/1048576) . 'MB)';
                }

                
                //image_4
                if(! empty($pic4Name) && ! in_array($dump4, ALLOWED_EXTENSIONS)) {
                    $formErrors[] = 'Image#_4 extension is not allowed <strong> !</strong>';
                }

                if( empty($pic4Name)) {
                    $formErrors[] = 'Image#_4 can not be<strong> empty !</strong>';
                }

                if ($pic4Size > MAX_FILE_SIZE) {
                    $formErrors[] = 'Image#_4 file is too large (max ' . (MAX_FILE_SIZE/1048576) . 'MB)';
                }

                //image_5
                
                if(! empty($pic5Name) && ! in_array($dump5, ALLOWED_EXTENSIONS)) {
                    $formErrors[] = 'Image#_5 extension is not allowed <strong> !</strong>';
                }
                
                if( empty($pic5Name)) {
                    $formErrors[] = 'Image#_5 can not be<strong> empty !</strong>';
                }
                
                if ($pic5Size > MAX_FILE_SIZE) {
                    $formErrors[] = 'Image#_5 file is too large (max ' . (MAX_FILE_SIZE/1048576) . 'MB)';
                }

                foreach($formErrors as $error) {
                    echo '<div class="alert alert-danger">' . $error . '</div>';

                }


                // check if no error update database 
                if (empty($formErrors)) {
               
                    // Process all images using the function
                    // $p1 = processImageUpload('pic1');
                    // $p2 = processImageUpload('pic2');
                    // $p3 = processImageUpload('pic3');
                    // $p4 = processImageUpload('pic4');
                    // $p5 = processImageUpload('pic5');
                    
                    // Check if any uploads failed
                    // if($p1 === false || $p2 === false || $p3 === false || $p4 === false || $p5 === false) {
                    //     $formErrors[] = 'One or more image uploads failed. Please check file types and sizes.';
                    // }
                    

                // image 1             
                $p1 = rand(0, 100000000) . '_' . $pic1Name;
                move_uploaded_file($pic1Tmp, UPLOAD_PATH . $p1);

                // //image 2
                $p2 = rand(0, 100000000) . '_' . $pic2Name;
                move_uploaded_file($pic2Tmp, UPLOAD_PATH . $p2);

                // //image 3
                $p3 = rand(0, 100000000) . '_' . $pic3Name;
                move_uploaded_file($pic3Tmp, UPLOAD_PATH . $p3);

                // //image 4
                $p4 = rand(0, 100000000) . '_' . $pic4Name;
                move_uploaded_file($pic4Tmp, UPLOAD_PATH . $p4);

                // //image 5
                $p5 = rand(0, 100000000) . '_' . $pic5Name;
                move_uploaded_file($pic5Tmp, UPLOAD_PATH . $p5);

                // Insert Item info in database 
                $stmt = $con->prepare("INSERT INTO 
                    items(Name, Brand, Description, tags, sectionID, Cat_ID, subcat, Price, priceBefore, discount, hotDeal, trend, Rating, Add_Date, Member_ID, pic1, pic2, pic3, pic4, pic5)
                    VALUES
                    (:zname, :zbrand, :zdesc, :ztags, :zsection, :zcat, :zsubcat, :zprice, :zpricebefore, :zdiscount, :zhotdeal,  :ztrend, :zrate, now(), :zmember, :zpic1, :zpic2, :zpic3, :zpic4, :zpic5)");
                        
                $stmt->execute(array(
                    'zname'     => $name,
                    'zbrand'    => $brand,
                    'zdesc'     => $desc,
                    'ztags'     => $tags,
                    'zsection'  => $section,
                    'zcat'      => $cat,
                    'zsubcat'   => $subcat,
                    'zprice'    => $price,
                    'zpricebefore' => $pricebefore,
                    'zdiscount' => $discount,
                    'zhotdeal'  => $hotdeal,
                    'ztrend'    => $trend,
                    'zrate'     => $rating,
                    'zmember'   => $member,
                    'zpic1'     => $p1,
                    'zpic2'     => $p2,
                    'zpic3'     => $p3,
                    'zpic4'     => $p4,
                    'zpic5'     => $p5

                    ));
                    // // Echo success message
                    $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record inserted</div>';
                    redirectHome($theMsg, 'back');
                    }

                } else {
                    echo "<div class='container'>";

                    $theMsg = '<div class="alert alert-danger"> sorry you can not browse this page directly </div>';

                    redirectHome($theMsg);

                    echo "</div>";
                }

            echo "</div>";
            



        } elseif ($do == 'Edit') {

            $itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;
            
            $stmt = $con->prepare("SELECT * FROM items WHERE Item_ID = ? ");

            $stmt->execute(array($itemid));

            $item = $stmt->fetch();
            
            $count = $stmt->rowCount();

            if($stmt->rowCount() > 0) { 
                
                ?>
                <h5 class="text-left"> Welcome : <?php echo $_SESSION['Username']?> </h5>
                <h1 class="text-center"> Edit Item </h1>

                <div class="container">

                    <form action="?do=Update" method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="itemid" value="<?php echo $itemid ?>">

                        <div class="row">

                            <div class="dataInput">

                                <!-- start name field  -->
                                <div class="form-group">
                                    <label> Name </label>
                                    <div class="inputname">
                                        <input type="text" 
                                        name="name" 
                                        class="form-control" 
                                        required="required" 
                                        placeholder=" Name of item"
                                        value=" <?php echo $item ['Name']?>"/>
                                    </div>
                                </div>
                                <!-- End name field  -->

                                <!-- start Brand field  -->
                                <div class="form-group">
                                    <label> Brand </label>
                                    <div class="inputname">
                                        <input type="text"
                                        name="brand" 
                                        class="form-control" 
                                        required="required" 
                                        placeholder=" brand of item"
                                        value="<?php echo $item['Brand'] ?>"/>
                                    </div>
                                </div>
                                <!-- End Brand field  -->

                                <!-- start description field  -->
                                <div class="form-group">
                                    <label> Description </label>
                                    <div class="inputname">
                                        <input type="text" 
                                        name="description" 
                                        class="form-control" 
                                        placeholder=" description of item"
                                        required="required"
                                        value=" <?php echo $item ['Description']?>"/>
                                    </div>
                                </div>
                                <!-- End description field  -->
                                
                                <!-- start Tags field  -->
                                <div class="form-group">
                                    <label> Tags </label>
                                    <div class="inputname">
                                        <input type="text" 
                                        name="tags" 
                                        class="form-control" 
                                        placeholder=" Tags describe your ads, separate tags with comma , "
                                        required="required"
                                        value="<?php echo $item['tags'] ?>"/>
                                    </div>
                                </div>
                                <!-- End Tags field  -->



                                    <div class="indentity">
                        
                                        <!-- start Section field  -->
                                        <div class="form-group">
                                            <div class="inputname">
                                            <label> Section </label>
                                                <select class="selected" name="section">
                                                <option value="0" <?php if (isset($item['sectionID']) && $item['sectionID'] == 0) { echo 'selected'; } ?>>...</option>

                                                <?php
                                                $allCats = getAllfrom("*", "cat", "where parent = 0 AND section = 1", "", "ID");

                                                foreach ($allCats as $cat) {
                                                    // Parent category
                                                    echo "<option value='" . $cat['ID'] . "' ";
                                                    if (isset($item['sectionID']) && $item['sectionID'] == $cat['ID']) {
                                                        echo "selected";
                                                    }
                                                    echo ">" . $cat['Name'] . "</option>";

                                               
                                                }
                                                ?>

                                                </select>
                                            </div>
                                        </div>
                                        <!-- End Section field  -->
                                        
                                        <!-- start Categories field  -->
                                        <div class="form-group">
                                            <div class="inputname">
                                                <label> Category </label>
                                                <select class="selected" name="category">
                                                    <option value="0"<?php if ($item['Cat_ID'] == 0) { echo 'selected';} ?>>...</option>
                                                    <?php
                                                    $allCats = getAllfrom("*", "cat", "where parent = 0", "", "ID");
                                                    
                                                    foreach($allCats as $cat) {
                                                        echo "<option value='" . $cat['ID'] . "' ";
                        
                                                        if ($item['Cat_ID'] == $cat['ID']) { echo 'selected';}
                                                        
                                                        echo ">" . $cat['Name'] . "</option>";
                                                        
                                                        
                                                        
                                                        
                                                        }
                                                        ?>

                                                </select>
                                        
                                            </div>
                                        </div>
                                        <!-- End Categories field  -->

                                        
                                        <!-- start Sub Categories field  -->
                                        <div class="form-group">
        
                                            <div class="inputname">
                                                <label>Sub-Category </label>
                                                <select class="selected" name="subcategory">
                                                <option value="0"<?php if ($item['subcat'] == 0) { echo 'selected';} ?>>...</option>
                                                    
                                                    <?php
                                                    $allSCats = getAllfrom("*", "cat", "where parent != 0", "", "ID");

                                                    
                                                    foreach ($allSCats as $scat) {
                                                        echo "<option value='" . $scat['ID'] . "'";
                                                    
                                                        // $allCatsss = getAllfrom("*", "cat", "where parent = " . intval($scat['ID']), "", "ID");

                                                        // foreach ($allCatsss as $cat) {
                                                            if ($item['subcat'] == $scat['ID']) {
                                                                        echo ' selected';
                                                                        // break;
                                                                    }
                                                            // }
                                                    
                                                            


                                                        echo ">" . $scat['Name'] . "</option>";
                                                    }
                                                        ?>

                                                </select>

                                            </div>
                                        </div>
                                        <!-- End Sub Categories field  -->

                                        
                                    </div>            
                                
                                    <div class="pricearea">

                                        <!-- start price field  -->
                                        <div class="form-group">
                                            <label> Price </label>
                                            <div class="inputname">
                                                <input type="text" 
                                                name="price" 
                                                class="form-control" 
                                                placeholder=" item price"
                                                value="<?php echo $item ['Price']?>"/>
                                            </div>
                                        </div>
                                        <!-- End price field  -->
                            
                                        

                            


                                        
                                        <!-- start pricebefore field  -->
                                        <div class="form-group">
                                            <label>Price befor disc%</label>
                                            <div class="inputname">
                                                <input type="text" 
                                                name="pricebefore" 
                                                class="form-control" 
                                                placeholder="Price before discount"
                                                value="<?php echo $item ['priceBefore']?>"/>
                                            </div>
                                        </div>
                                        <!-- End pricebefore field  -->
                                        

                                        <!-- start Discount field  -->
                                        <div class="form-group">
                                            <label> Discount </label>
                                            <div class="inputname">
                                                <input type="text" 
                                                name="discount" 
                                                class="form-control" 
                                                placeholder=" item discount"
                                                value="<?php echo $item ['discount']?>"/>
                                            </div>
                                        </div>
                                        <!-- End Discount field  -->



                                    </div>


                                    <div class="conditionarea">
                                    
                            
                                        <!-- start Hot deals field  -->
                                        <div class="form-group">
                                            <div class="custom-select">
                                            <label> Hot deal </label>
                                                <select class="selected" name="hotdeals">
                                                    <option value="0"<?php if ($item['hotDeal'] == 0) { echo 'selected';} ?>>No </option>
                                                    <option value="1"<?php if ($item['hotDeal'] == 1) { echo 'selected';} ?>>Hot Deal</option>

                                                </select>
                                        
                                            </div>
                                        </div>
                                        <!-- End Hot seals field   -->

                                        
                                        
                                        <!-- start Trend field  -->
                                        <div class="form-group">
                                            <div class="custom-select">
                                            <label> Trend </label>
                                                <select class="selected" name="trend">
                                                    <option value="0"<?php if ($item['trend'] == 0) { echo 'selected';} ?>>No </option>
                                                    <option value="1"<?php if ($item['trend'] == 1) { echo 'selected';} ?>> Trend </option>

                                                </select>
                                        
                                            </div>
                                        </div>
                                        <!-- End Trend field   -->


                                        <!-- start rating field  -->
                                        <div class="form-group">
                                            <div class="custom-select">
                                            <label> Rating </label>
                                                <select name="rating" id="">
                                                    <option value="0"<?php if ($item['Rating'] == 0) { echo 'selected';} ?>>  ---  </option>
                                                    <option value="1"<?php if ($item['Rating'] == 1) { echo 'selected';} ?>>*</option>
                                                    <option value="2"<?php if ($item['Rating'] == 2) { echo 'selected';} ?>>**</option>
                                                    <option value="3"<?php if ($item['Rating'] == 3) { echo 'selected';} ?>>***</option>
                                                    <option value="4"<?php if ($item['Rating'] == 4) { echo 'selected';} ?>>****</option>
                                                    <option value="5"<?php if ($item['Rating'] == 5) { echo 'selected';} ?>>*****</option>
                                                </select>

                                            
                                            </div>
                                        </div>
                                        <!-- End rating field  -->



                                    </div>



                            </div>


                            <div class="picInput">
                                

                                <!-- start edit pictures  -->

                                
                                
                                <!-- start Items pictures field  -------Image 1-----------          -->
                                <div class="hero">
                                    <label for="inputpic"> Change image 1 </label>
                                    <img src="uploads/items/<?php echo $item['pic1'] ?>" alt="" id="prod-pic">
                                    <div class="prodcard">
                                        <input type="file" name="pic1" class="form-control" id="inputpic" value="<?php echo $item['pic1'] ?>"/>
                                        <input type="hidden" name="pic1dist" value="<?php echo $item['pic1'] ?>">
                                    </div>
                                </div>
                                <!-- End Items pictures field  -------Image 1-----------            -->        
                                

                                <!-- start Items pictures field  -------Image 2-----------          -->
                                <div class="hero">
                                    <label for="inputpicB"> Change image 2 </label>
                                    <img src="uploads/items/<?php echo $item['pic2'] ?>" alt="" id="prodpicB">
                                    <div class="prodcard">
                                        <input type="file" name="pic2" class="form-control"  id="inputpicB"/>
                                        <input type="hidden" name="pic2dist"  value="<?php echo $item['pic2'] ?>">
                                    </div>
                                </div>
                                <!-- End Items pictures field  -------Image 2-----------            -->        
                                

                                <!-- start Items pictures field  -------Image 3-----------          -->
                                <div class="hero">
                                    <label for="inputpicC"> Change image 3 </label>
                                    <img src="uploads/items/<?php echo $item['pic3'] ?>" alt="" id="prodpicC">
                                    <div class="prodcard">
                                    <input type="file" name="pic3" class="form-control" id="inputpicC" />
                                    <input type="hidden" name="pic3dist" value="<?php echo $item['pic3'] ?>"/>
                                    </div>
                                </div>
                                <!-- End Items pictures field  -------Image 3-----------            -->        


                                <!-- start Items pictures field  -------Image 4-----------          -->
                                <div class="hero">
                                    <label for="inputpicD"> Change image 4 </label>
                                    <img src="uploads/items/<?php echo $item['pic4'] ?>" alt="" id="prodpicD">
                                    <div class="prodcard">
                                        <input type="file" name="pic4" class="form-control" id="inputpicD"/>
                                        <input type="hidden" name="pic4dist" value="<?php echo $item['pic4'] ?>"/>
                                    </div>
                                </div>
                                <!-- End Items pictures field  -------Image 4-----------            -->        
                                


                                <!-- start Items pictures field  -------Image 5-----------          -->
                                <div class="hero">
                                    <label for="inputpicE"> Change image 5 </label>
                                    <img src="uploads/items/<?php echo $item['pic5'] ?>" alt="" id="prodpicE">
                                    <div class="prodcard">
                                        <input type="file" name="pic5" class="form-control" id="inputpicE" value="<?php echo $item['pic5'] ?>"/>
                                        <input type="hidden" name="pic5dist" value="<?php echo $item['pic5'] ?>"/>
                                    </div>
                                </div>
                                <!-- End Items pictures field  -------Image 5-----------            -->        
                                

                            </div>
                        </div>

                        <!-- start Submit/Save btn  -->
                        <div class="form-group  form-group-lg">
                            <div class="col-sm-offset-2 col-sm-10">
                                <input type="submit" value="Update" class="btn btn-primary btn-sm" />
                            </div>
                        </div>
                        <!-- End Submit/Save btn  -->

                    </form>

                        <!-- comment section -->
                        
                    <?php
                        $stmt = $con->prepare("SELECT comments.*, users.Username AS Member
                        FROM comments
                        INNER JOIN
                            users
                        ON
                            users.UserID = comments.user_id
                        WHERE
                            item_id = ? ");

                        $stmt->execute(array($itemid));
                        $rows = $stmt->fetchAll();

                        if(! empty($row)) {

                        ?>



                        <h1 class="text-center"> Manage [ <?php echo $item ['Name'] ?> ] comments </h1>

                            <div class="table-responsive">
                                <table class="main-table text-center table table-boardered">
                                    <tr>
                                        <td>Comment</td>
                                        <td>User name</td>
                                        <td>Added date</td>
                                        <td>Control</td>
                                    </tr>
                                    <?php
                                    foreach($rows as $row) {
                                        echo "<tr>";
                                            echo "<td>" . $row['comment'] . "</td>";
                                            echo "<td>" . $row['Member'] . "</td>";
                                            echo "<td>" . $row['comment_date'] . "</td>";
                                            echo "<td>
                                            <a href='comments.php?do=Edit&comid=" . $row['c_id'] . "' class='btn btn-success'><i class='fa fa-edit'></i> Edit </a>
                                            <a href='comments.php?do=Delete&comid=" . $row['c_id'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Delete </a>";
                                            
                                            if ($row['status'] == 0){
                                                echo "<a href='comments.php?do=Approve&comid=" . $row['c_id'] . "' class='btn btn-info activate'><i class='fa fa-close'></i> Approve </a>";
                                        
                                            }


                                            echo "</td>";
                                        echo "</tr>";
                                        
                                    }
                                
                                    ?>
                                </table>
                            </div>
                            <?php } ?>
                </div>

                <?php   
        

            } else {

                echo "<div class='container'>";
            
                $theMsg = '<div class="alert alert-danger">there is no such ID</div>';

                redirectHome($theMsg);

                echo  "</div>";
            }


        } elseif ($do == 'Update') {

            echo "<h1 class='text-center'> Update Item </h1>";
            echo "<div class='container'>";
        

            if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        
                // get variables from from 
                $id             = $_POST['itemid'];
                $name           = $_POST['name'];
                $brand          = $_POST['brand'];
                $desc           = $_POST['description'];
                $tags           = $_POST['tags'];
                $section        = $_POST['section'];
                $cat            = $_POST['category'];
                $subcat         = $_POST['subcategory'];
                $price          = $_POST['price'];
                $pricebefore    = $_POST['pricebefore'];
                $discount       = $_POST['discount'];
                $hotdeal        = $_POST['hotdeals'];
                $trend          = $_POST['trend'];
                $rating         = $_POST['rating'];
                $member         = $_SESSION['ID'];


                $pic1 = $_FILES['pic1'];
                $pic1dist = $_POST['pic1dist'];
                $pic1NameRe = $_FILES['pic1']['name'];
                $pic1Tmp = $_FILES['pic1']['tmp_name'];



                $pic2 = $_FILES['pic2'];
                $pic2dist = $_POST['pic2dist'];
                $pic2NameRe = $_FILES['pic2']['name'];
                $pic2Tmp = $_FILES['pic2']['tmp_name'];


                $pic3 = $_FILES['pic3'];
                $pic3dist = $_POST['pic3dist'];
                $pic3NameRe = $_FILES['pic3']['name'];
                $pic3Tmp = $_FILES['pic3']['tmp_name'];


                $pic4 = $_FILES['pic4'];
                $pic4dist = $_POST['pic4dist'];
                $pic4NameRe = $_FILES['pic4']['name'];
                $pic4Tmp = $_FILES['pic4']['tmp_name'];


                $pic5 = $_FILES['pic5'];
                $pic5dist = $_POST['pic5dist'];
                $pic5NameRe = $_FILES['pic5']['name'];
                $pic5Tmp = $_FILES['pic5']['tmp_name'];

                // validate the form 
        
            $formErrors = array();

            
                
            if(empty($name)) {
                $formErrors[] = 'name can not be<strong> empty !</strong>';

            }

            if(empty($desc)) {
                $formErrors[] = 'description can not be<strong> empty !</strong>';

            }

            if (empty($price)) {
                $formErrors[] = 'price can not be <strong> empty !</strong>';
            }
                    
            if ($cat == 0) {
                $formErrors[] = 'you must choose the <strong> category !</strong>';
            }
            
                // check if no error update database 
        
                if (empty($formErrors)) {
        


                    if($_FILES['pic1']['error'] == UPLOAD_ERR_NO_FILE){

                        // echo 'no file 1';
                        $pp1 = $pic1dist;

                    } else {
                        $pp1 = rand(0, 100000000) . '_' . $pic1NameRe;
                        move_uploaded_file($pic1Tmp, UPLOAD_PATH . $pp1);
                    }
        

                    if($_FILES['pic2']['error'] == UPLOAD_ERR_NO_FILE){

                        // echo 'no file 2';
                        $pp2 = $pic2dist;

                    } else {
                        $pp2 = rand(0, 100000000) . '_' . $pic2NameRe;
                        move_uploaded_file($pic2Tmp, UPLOAD_PATH . $pp2);
                    }



                    if($_FILES['pic3']['error'] == UPLOAD_ERR_NO_FILE){

                        // echo 'no file 3';
                        $pp3 = $pic3dist;

                    } else {
                        $pp3 = rand(0, 100000000) . '_' . $pic3NameRe;
                        move_uploaded_file($pic3Tmp, UPLOAD_PATH . $pp3);
                    }



                    if($_FILES['pic4']['error'] == UPLOAD_ERR_NO_FILE){

                        // echo 'no file 4';
                        $pp4 = $pic4dist;

                    } else {
                        $pp4 = rand(0, 100000000) . '_' . $pic4NameRe;
                        move_uploaded_file($pic4Tmp, UPLOAD_PATH . $pp4);
                    }



                    if($_FILES['pic5']['error'] == UPLOAD_ERR_NO_FILE){

                        // echo 'no file 5';
                        $pp5 = $pic5dist;

                    } else {
                        $pp5 = rand(0, 100000000) . '_' . $pic5NameRe;
                        move_uploaded_file($pic5Tmp, UPLOAD_PATH . $pp5);
                    }



                // update database 
            
                $stmt = $con->prepare("UPDATE 
                                            items 
                                        SET
                                            Name = ?,
                                            Brand = ?,
                                            Description = ?, 
                                            tags = ?,
                                            sectionID = ?,
                                            Cat_ID = ?,
                                            subcat = ?,
                                            Price = ?,
                                            priceBefore = ?,
                                            discount = ?,
                                            hotDeal = ?,
                                            trend = ?,
                                            Rating = ?,
                                            Member_ID = ?,
                                            pic1 = ?,
                                            pic2 = ?,
                                            pic3 = ?,
                                            pic4 = ?,
                                            pic5 = ?
                                        WHERE 
                                            Item_ID = ?");
                                            
                $stmt->execute(array($name, $brand, $desc, $tags, $section, $cat, $subcat, $price, $pricebefore, $discount, $hotdeal, $trend, $rating, $member, $pp1, $pp2, $pp3, $pp4, $pp5, $id));
                
                // // Echo success message
        
                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';
        
                redirectHome($theMsg, 'back');
        

                }
        
        
            } else {
        
                $theMsg ='<div class="alert alert-danger">sorry you can not browse this page directly</div>';
        
                // redirectHome($theMsg);

                print_r($id);
            }
        
            echo "</div>";
        


        } elseif ($do == 'Delete') {
            
            echo "<h1 class='text-center'> Delete Item </h1>";
            echo "<div class='container'>";

                $itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;
                
                $check = checkItem('Item_ID', 'items', $itemid);

                if($check > 0) { 

                    $stmt = $con->prepare("DELETE FROM items WHERE Item_ID = :zid");

                    $stmt->bindParam(":zid", $itemid);

                    $stmt->execute();

                    $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

                    redirectHome($theMsg, 'back');

                } else {

                    $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                    redirectHome($theMsg);

                    }

            echo '</div>';



        } elseif ($do == 'Approve') {

            echo "<h1 class='text-center'> Approve Item </h1>";
            echo "<div class='container'>";
        
            // check if GET request Item ID is Numeric and get the integer value 

                    $itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;
                    
                    // select all Data depend on this ID
                    $check = checkItem('Item_ID', 'items', $itemid);
        
                    if($check > 0) { 
    
                        $stmt = $con->prepare("UPDATE items SET Approve = 1 WHERE Item_ID = ?");
        
                        $stmt->execute(array($itemid));
        
                        $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Activated</div>';
        
                        redirectHome($theMsg, 'back');
        
                    } else {

                        $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                        redirectHome($theMsg);
        
                    }
        
            echo '</div>';
        

        } elseif ($do == 'Hide') {

            echo "<h1 class='text-center'> Hide Item </h1>";
            echo "<div class='container'>";
        
                // check if GET request Item ID is Numeric and get the integer value 
                $itemid = isset($_GET['itemid']) && is_numeric($_GET['itemid']) ? intval($_GET['itemid']) : 0;
                        
                // select all Data depend on this ID
                $check = checkItem('Item_ID', 'items', $itemid);

                if($check > 0) { 
                    $stmt = $con->prepare("UPDATE items SET Approve = 0 WHERE Item_ID = ?");

                    $stmt->execute(array($itemid));

                    $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Activated</div>';

                    redirectHome($theMsg, 'back');
                }else {

                    $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                    redirectHome($theMsg);
                    }
            
            echo '</div>';
        


        }

        include $tpl . 'footer.php';

    } else {
        header('Location: index.php');

        exit();

}
    ob_end_flush();

?>
