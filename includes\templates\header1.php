<!DOCTYPE html>
<html>
    <head>
        <meta charet ="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title><?php getTitle() ?></title>    
        <link rel="stylesheet" href="<?php echo $css; ?>bootstrap.min.css" />
        <link rel="stylesheet" href="<?php echo $css; ?>font-awesome.min.css" />
        <link rel="stylesheet" href="<?php echo $css; ?>jquery-ui.css" />
        <link rel="stylesheet" href="<?php echo $css; ?>jquery.selectBoxIt.css" />
        <link rel="stylesheet" href="<?php echo $css; ?>css/style.css" />

        
        <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"> -->
        <!-- <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous"> -->
        <!-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script> -->


        
        <!-- swiper cdn  -->
        <link rel="stylesheet"  href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>
        <!-- font awesome cdn link -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css">
        <!-- custom css file -->
        <link rel="stylesheet" href="css/style.css">

    </head>
<body>




<!-- header section starts -->
<header class="header">



<a href="#" class="logo"> <i class="fa-solid fa-laptop"> </i> Tikmart </a>

<nav class="navbar">
    <a href="#home">home</a>
    <a href="#features">features</a>
    <a href="#products">products</a>
    <a href="#categories">categories</a>
    <a href="#review">review</a>
    <a href="#blogs">blogs</a>

</nav>

<div class="icons">

    <div class="fas fa-bars" id="menu-btn"></div>
    <div class="fas fa-search" id="search-btn"></div>
    <div class="fas fa-shopping-cart" id="cart-btn"></div>
    <div class="fas fa-user" id="login-btn"></div>
    
    
</div>

<form action="" class="search-form">
    <input type="search" id="search-box" placeholder="search here. . .">
    <label for="search-box" class="fas fa-search"></label>

</form>


<div class="shopping-cart">
    <div class="box">
        <i class="fas fa-trash"></i>
        <img src="img/cart-img-1.png" alt="">
        <div class="content">
            <h3>Watermelon</h3>
                <span class="price">$4.99/-</span>
                <span class="quantity">qty : 1</span>
        </div>
    </div>




    <div class="box">
        <i class="fas fa-trash"></i>
        <img src="img/cart-img-2.png" alt="">
        <div class="content">
            <h3>Onion</h3>
                <span class="price">$4.99/-</span>
                <span class="quantity">qty : 1</span>
        </div>
    </div>




    <div class="box">
        <i class="fas fa-trash"></i>
        <img src="img/cart-img-3.png" alt="">
        <div class="content">
            <h3>Chiken</h3>
                <span class="price">$4.99/-</span>
                <span class="quantity">qty : 1</span>
        </div>
    </div>

    <div class="total">total : $19.99/-</div>
    <a href="#" class="btn">checkout</a>



    
</div>

<form action="" class="login-form">

    <h3>login now</h3>
    <input type="email" placeholder="your email" class="box">
    <input type="password" placeholder="your password" class="box">
    <p>forget your password <a href="#">click here</a></p>
    <p>don't have an account <a href="#">create new</a></p>
    <input type="submit" value="login now" class="btn">

</form>



<!-- 




<div class="mnav">

<div class="upper-bar">
    <div class="container text-right">
        <?php
        if(isset($_SESSION['user'])) { ?>

        <img class="my-image img-thumbnail img-circle" src="img/product-1.png" alt="" >

        <div class="btn-group my-info pull-right">
            <span class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                <?php echo $sessionUser ?>
                <span class="cart"></span>
            </span>
            <ul class="dropdown-menu">
            <li><a href="profile.php"></a> My profile </li>
            <li><a href="newad.php"></a> Add new item </li>
            <li><a href="profile.php#my-ads"></a> My items </li>
            <li><a href="logout.php"></a> Logout </li>
            </ul>
        </div>
        <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">Ehab <span class="caret"></span></a>
        <?php

        } else {

    ?>
        <a href="login.php">
            <span class="pull-right">Login/Signup</span>
        </a>
        <?php } ?>
    </div>
</div>





<nav class="navbar">

<div class="container">
    <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" 
        data-toggle="collapse" data-target="#app-nav" aria-expanded="false">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        </button>

        <a href="index.php" class="navbar-brand logo"> <i class="fa-solid fa-laptop"></i> Tikmart </a>


    </div>
    <div class="collapse navbar-collapse" id="app-nav">
        <ul class="nav navbar-nav navbar-right">
            <?php
            $allCats = getAllfrom("*","cat","where parent = 0","","ID","ASC");
                foreach($allCats as $cat) {
                    echo
                    '<li><a href="cat.php?pageid=' . $cat['ID'] . ' ">
                    ' . $cat['Name'] . '</a></li>';
                }
            ?>

        </ul>
    </div>
</div>

</nav>



</div> -->

</header>



















<!-- 
<div class="icons">

    <div class="fas fa-bars" id="menu-btn"></div>
    <div class="fas fa-search" id="search-btn"></div>
    <div class="fas fa-shopping-cart" id="cart-btn"></div>
    <div class="fas fa-user" id="login-btn"></a></div>
    
    
</div>




<form action="" class="search-form">
    <input type="search" id="search-box" placeholder="search here. . .">
    <label for="search-box" class="fas fa-search"></label>

</form>


<div class="shopping-cart">
    <div class="box">
        <i class="fas fa-trash"></i>
        <img src="img/cart-img-1.png" alt="">
        <div class="content">
            <h3>Watermelon</h3>
                <span class="price">$4.99/-</span>
                <span class="quantity">qty : 1</span>
        </div>
    </div>
        
    <div class="total">total : $19.99/-</div>
    <a href="#" class="btn">checkout</a>

    
</div>


<form class="login-form" action="

//<//?php echo $_SERVER['PHP_SELF']?>


" method="POST">

    <h3>login now</h3>
    <div class="input-container">
    <input class="form-control" type="text" name="username" autocomplete="off" placeholder="enter your username" required>

    </div>

    <div class="input-container">
    <input class="form-control" type="password" name="password" autocomplete="new-password" placeholder="enter your password" required>
    </div>
    
    <p>forget your password <a href="#">click here</a></p>
    <p>don't have an account <a href="signup.php">create new</a></p>
    <input type="submit" value="login now" class="btn">

</form>



</div>

-->

<!-- header section ends -->



<!-- home section started -->


<!-- <section class="home" id="home">


</section> -->


<!-- home section ends -->


