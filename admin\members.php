<?php

// Manage members page

// you can Add | Edit | Delete members here 

session_start();

ob_start();

$pageTitle = 'Members';

if(isset($_SESSION['Username'])) {
    
    include 'init.php';
    
    $do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

    // Start manage page 
    if($do == 'Manage') {// Manage members page

        $query = ''; 

        if (isset($_GET['page']) && $_GET['page'] == 'Pending') {
            $query = 'AND RegStatus = 0';
        }

        $stmt = $con->prepare("SELECT * FROM users WHERE GroupID !=1 $query ORDER BY UserID DESC");
        $stmt->execute();
        $rows = $stmt->fetchAll();

        if(! empty($rows)) {
        ?>

        <h1 class="text-center"> Manage members </h1>
        <a href="members.php?do=Add" class="btn btn-sm btn-primary"> <i class="fa fa-plus"></i> Add new member </a>
        <div class="container">
            <div class="table-responsive">
                <table class="main-table manage-members text-center table table-boardered">
                    <tr>
                        <td>#ID</td>
                        <td>Avatar</td>
                        <td>Username</td>
                        <td>Email</td>
                        <td>Full name</td>
                        <td>Register date</td>
                        <td>Control</td>
                    </tr>
                    <?php
                    foreach($rows as $row) {
                        echo "<tr>";

                            echo "<td>" . $row['UserID'] . "</td>";
                        
                            echo "<td><img src= 'uploads/userdefault.jpg' alt=''/></td>";

                            echo "<td>" . $row['Username'] . "</td>";
                            
                            echo "<td>" . $row['Email'] . "</td>";
                            
                            echo "<td>" . $row['FullName'] . "</td>";
                            
                            echo "<td>" . $row['Date'] . "</td>";
                            
                            echo "<td>
                            <a href='members.php?do=Edit&userid=" . $row['UserID'] . "' class='btn btn-success'><i class='fa fa-edit'></i> Edit </a>
                            <a href='members.php?do=Delete&userid=" . $row['UserID'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Delete </a>";
                            
                if ($row['RegStatus'] == 0){
                    echo "<a href='members.php?do=Activate&userid=" . $row['UserID'] . "' class='btn btn-info activate'><i class='fa fa-close'></i> Activate </a>";
            
                }


                                    echo "</td>";
                                echo "</tr>";
                                
                            }

                            
                            ?>

                        </table>


                    </div>


                </div>

        <?php 
        } else {

                echo '<div class="container">';
                echo '<div class="nice-message"> there is no records to show </div>';
                echo '<a href="members.php?do=Add" class="btn btn-primary">
                <i class="fa fa-plus"></i> Add new member </a>';
                echo '</div>';
            
            }
        
        ?>

        <?php

} elseif ($do == 'Add') { // Add Members page 

    ?>
    <h1 class="text-center"> New member </h1>
    <div class="container">
        <form class="members" action="?do=Insert" method="POST" enctype="multipart/form-data">
        <div class="row">

            <div class="dataInpunt">
            
                <!-- start Username field  -->
                <div class="form-group">
                    <label>Username</label>
                    <div class="inputname">
                        <input type="text" 
                        name="username" 
                        class="form-control" 
                        autocomplete="off" 
                        
                        placeholder="username to login shop"/>
                    </div>
                </div>
                <!-- End Username field  -->


                <!-- start Password field  -->
                <div class="form-group">
                    <label>Password</label>
                    <div class="inputname">
                        <input type="password" 
                        name="password" 
                        class="password form-control" 
                        autocomplete="new-password" 
                        required="required" 
                        placeholder="password must be complex"/>
                        <i class="show-pass fa fa-eye"></i>
                    </div>
                </div>
                <!-- End Password field  -->

                <!-- start Email field  -->
                <div class="form-group">
                    <label>Email</label>
                    <div class="inputname">
                        <input type="email" 
                        name="email" 
                        class="form-control" 
                        required="required" 
                        placeholder="please enter valid email"/>
                    </div>
                </div>
                <!-- End Email field  -->


                <!-- start Full name field  -->
                <div class="form-group">
                    <label>Full name</label>
                    <div class="inputname">
                        <input type="text" 
                        name="full" 
                        class="form-control" 
                        required="required" 
                        placeholder="name will appear in profile page"/>
                    </div>
                </div>
                <!-- End Full name field  -->

            </div>

            <!-- start Avatar pictures field  -->
             
            <div class="picInput">
                 
                <div class="hero">
                    <!-- <label for="inputpicF"> Upload image </label> -->
                    <img src="uploads/userdefault.jpg" alt="" id="prodpicF">
                    <div class="prodcard">
                        <!-- <input type="file" name="avatar" class="form-control" id="inputpicF"/> -->
                    </div>
                </div>
            
            </div>


            <!-- end Avatar pictures field  -->

        </div>


        <!-- start Submit/Save btn  -->
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <input type="submit" value="Add member" class="btn btn-primary btn-lg" />
            </div>
        </div>
        <!-- End Submit/Save btn  -->




        </form>
    </div>
    <?php

} elseif ($do == 'Insert') {  // Insert member page
    

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {

        echo "<h1 class='text-center'> Insert Member </h1>";
        echo "<div class='container'>";



        // upload variables
 
        // $avatarName = $_FILES['avatar']['name'];
        // $avatarSize = $_FILES['avatar']['size'];    
        // $avatarTmp  = $_FILES['avatar']['tmp_name'];
        // $avatarType = $_FILES['avatar']['type'];

        // list of allowed file types to upload
        // $avatartAllowedExtension = array("jpeg", "jpg", "bmp", "png", "gif");
        
        // get avatar extension
        // $tmp = explode('.', $avatarName);
        // $avatarExtension = strtolower(end($tmp));



        // get variables from from 

        $user   = $_POST['username'];
        $pass   = $_POST['password'];
        $email  = $_POST['email'];
        $name   = $_POST['full'];

        $hashPass = sha1($_POST['password']);

        
        // validate the form 

        $formErrors = array();

        if(strlen($user) < 4) {
            $formErrors[] = 'username can not be less than<strong> 4 char. !</strong>';

        }

        if(strlen($user) > 35) {
            $formErrors[] = 'username can not be more than <strong> 35 char. !</strong>';

        }

        if (empty($user)) {
            $formErrors[] = 'username can not be <strong> empty !</strong>';
        }
                
        if (empty($pass)) {
            $formErrors[] = 'password can not be <strong> empty !</strong>';
        }
        
        if (empty($name)) {
            $formErrors[] = 'full name can not be <strong> empty !</strong>';
        }

        
        if (empty($email)) {
            $formErrors[] = 'e-mail can not be <strong> empty !</strong>';
        }


        
        // if(! empty($avatarName) && ! in_array($avatarExtension, $avatartAllowedExtension)){
        //    $formErrors[] = 'file extension is not <strong> allwoed !</strong>';
        // }
        
        // set the size of the uploaded picture
        // if($avatarSize > 6822400){
        //     $formErrors[] = 'profile picture can not be bigger than <strong> 6.5 M byte !</strong>';
        // }


        foreach($formErrors as $error) {
            echo '<div class="alert alert-danger">' . $error . '</div>';

        }
        

        // check if no error update database 

        if (empty($formErrors)) {
            
            // $avatar = rand(0,1000000000) . '_' . $avatarName;
            // set location of saving file
            // move_uploaded_file($avatarTmp,"uploads\avatars\\" . $avatar);



            // check if user exist in database
            $check = checkItem("Username", "users", $user);
    
            if($check == 1) {
                $theMsg = '<div class="alert alert-danger">Sorry, this user is Exist</div>';

                redirectHome($theMsg, 'back');
            } else {

                // Insert user info in database 

                $stmt = $con->prepare("INSERT INTO 
                                            users(Username, Password, Email, FullName, RegStatus, Date)
                                            VALUES(:zuser, :zpass, :zmail, :zname, 0, now())");
                
                $stmt->execute(array(
                    'zuser' => $user,
                    'zpass' => $hashPass,
                    'zmail' => $email,
                    'zname' => $name
                    // 'zavatar' => $avatar

                ));

                // // Echo success message

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record inserted</div>';
                redirectHome($theMsg, 'back');

        }

        

    }

    } else {
        echo "<div class='container'>";

        $theMsg = '<div class="alert alert-danger"> sorry you can not browse this page directly </div>';

        redirectHome($theMsg);

        echo "</div>";
    }

    echo "</div>";
    


} elseif ($do == 'Edit') {  // Edit page 

    $userid = isset($_GET['userid']) && is_numeric($_GET['userid']) ? intval($_GET['userid']) : 0;
     
    $stmt = $con->prepare("SELECT * 
                            FROM users 
                            WHERE UserID = ? 
                            LIMIT 1");
    $stmt->execute(array($userid));
    $row = $stmt->fetch();
    $count = $stmt->rowCount();

    if($stmt->rowCount() > 0) { ?>

            <h1 class="text-center"> Edit Member </h1>
            <div class="container">
                <form class="edituser" action="?do=Update" method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="userid" value="<?php echo $userid ?>"/>
                    <div class="row">

                        <div class="dataInpunt">
                
                            <!-- start Username field  -->
                            <div class="form-group">
                                <label>Username</label>
                                <div class="inputfield">
                                    <input type="text" 
                                    name="username" 
                                    class="form-control" 
                                    value="<?php echo $row['Username'] ?>" 
                                    autocomplete="off" 
                                    required="required"/>
                                </div>
                            </div>
                            <!-- End Username field  -->
                        
                        
                            <!-- start Password field  -->
                            <div class="form-group">
                                <label>Password</label>
                                <div class="inputfield">
                                    <input type="hidden" name="oldpassword" value="<?php echo $row ['Password'] ?>"/>
                                    <input type="password" name="newpassword" class="form-control" autocomplete="new-password" placehplder="if you leave it same password will remain"/>
                                </div>
                            </div>
                            <!-- End Password field  -->
                        
                        
                            <!-- start Email field  -->
                            <div class="form-group">
                                <label>Email</label>
                                <div class="inputfield">
                                    <input type="email" 
                                    name="email" 
                                    value="<?php echo $row['Email'] ?>" 
                                    class="form-control" 
                                    required="required"/>
                                </div>
                            </div>
                            <!-- End Email field  -->


                            <!-- start Full name field  -->
                            <div class="form-group">
                                <label>Full name</label>
                                <div class="inputfield">
                                    <input type="text" 
                                    name="full" value="<?php echo $row['FullName'] ?>" 
                                    class="form-control" 
                                    required="required"/>
                                </div>
                            </div>
                            <!-- End Full name field  -->


                        </div>

                        <div class="picInput">
                            
                            <!-- start Avatar pictures field  -->
                            <div class="hero">
                                <!-- <label for="avatarNew2"> Upload image </label> -->
                                <img src="uploads/userdefault.jpg" alt="" id="avatarOld2" />
                                <div class="prodcard">
                                    <!-- <input type="file" name="avatar" class="form-control" id="avatarNew2" /> -->
                                    <!-- <input type="hidden" name="avatardist" value="<?php echo $row['avatar'] ?>" /> -->
                                </div>
                            </div>
                            <!-- end Avatar pictures field -->


                        </div>
                    </div>
            

                    <!-- start Submit/Save btn  -->
                    <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-6">
                                <input type="submit" value="Save" class="btn btn-primary btn-lg" />
                            </div>
                    </div>
                    <!-- End Submit/Save btn  -->
                

                </form>    

            </div>

    <?php
    

    } else {

        echo "<div class='container'>";
    
        $theMsg = '<div class="alert alert-danger">there is no such ID</div>';

        redirectHome($theMsg);

        echo  "</div>";
    }


} elseif ($do == 'Update') { // update page

    echo "<h1 class='text-center'> Update Member </h1>";
    echo "<div class='container'>";

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {



        
        
        // mine

        // get variables from from 
        $id     = $_POST['userid'];
        $user   = $_POST['username'];
        $email  = $_POST['email'];
        $name   = $_POST['full'];

        
        // $avatarName = $_FILES['avatar']['name'];
        // $avatardist = $_POST['avatardist'];
        // $avatarNameRe = $_FILES['avatar']['name'];
        // $avatarTmp  = $_FILES['avatar']['tmp_name'];

        // upload variables
 
        // $avatarSize = $_FILES['avatar']['size'];    
        // $avatarType = $_FILES['avatar']['type'];

        // list of allowed file types to upload
        // $avatartAllowedExtension = array("jpeg", "jpg", "bmp", "png", "gif");
        
        // get avatar extension
        // $tmp = explode('.', $avatarName);
        // $avatarExtension = strtolower(end($tmp));

        
        // Password trick - if not passwored entered it save same old password

        $pass = empty($_POST['newpassword']) ? $_POST['oldpassword'] : sha1($_POST['newpassword']);

        
        // validate the form 

        $formErrors = array();

        if(strlen($user) < 4) {
            $formErrors[] = 'username can not be less than<strong> 4 char. !</strong>';

        }

        if(strlen($user) > 35) {
            $formErrors[] = 'username can not be more than <strong> 35 char. !</strong>';

        }

        if (empty($user)) {
            $formErrors[] = 'username can not be <strong> empty !</strong>';
        }
        
        
        if (empty($name)) {
            $formErrors[] = 'full name can not be <strong> empty !</strong>';
        }

        
        if (empty($email)) {
            $formErrors[] = 'e-mail can not be <strong> empty !</strong>';
        }

        // mine 
        // if(! empty($avatarName) && ! in_array($avatarExtension, $avatartAllowedExtension)){
            // $formErrors[] = 'file extension is not <strong> allwoed !</strong>';
        // }
         
        // set the size of the uploaded picture
        // if($avatarSize > 6822400){
            // $formErrors[] = 'profile picture can not be bigger than <strong> 6.5 M byte !</strong>';
        // }
        
        foreach($formErrors as $error) {
            echo '<div class="alert alert-danger">' . $error . '</div>';

        }
        

        if (empty($formErrors)) {

            // if($_FILES['avatar']['error'] == UPLOAD_ERR_NO_FILE){

                // echo 'no file 1';
                // $pp1 = $avatardist;

            // } else {
                // $pp1 = rand(0, 100000000) . '_' . $avatarName;
                // move_uploaded_file($avatarTmp, "uploads/avatars\\" . $pp1);
            // }
  




        // check if no error update database 

            $stmt2 = $con->prepare("SELECT * FROM users WHERE Username = ? AND UserID != ?");
            $stmt2->execute(array($user, $id));
            $count = $stmt2->rowCount();

            if($count == 1) {
                

                echo '<div class="alert alert-danger">Sorry, this username already exist </div>';

                redirectHome($theMsg, 'back');

            } else {

            // update database 
        
                $stmt = $con->prepare("UPDATE users SET Username  = ?,
                Email =  ?, FullName=?, Password = ? WHERE UserID = ?");

                $stmt->execute(array($user, $email, $name, $pass , $id));

                // // Echo success message

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

                redirectHome($theMsg, 'back');

             
                
            }

        }


    } else {

        $theMsg ='<div class="alert alert-danger">sorry you can not browse this page directly</div>';

        redirectHome($theMsg);

    }

    echo "</div>";


} elseif ($do == 'Delete') {  // Delete member page


    
    echo "<h1 class='text-center'> Delete Member </h1>";
    echo "<div class='container'>";

            $userid = isset($_GET['userid']) && is_numeric($_GET['userid']) ? intval($_GET['userid']) : 0;
            
            $check = checkItem('userid', 'users', $userid);

            if($check > 0) { 


                $stmt = $con->prepare("DELETE FROM users WHERE UserID = :zuser");

                $stmt->bindParam(":zuser", $userid);

                $stmt->execute();

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

                redirectHome($theMsg, 'back');

                

            } else {

                $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                redirectHome($theMsg);

            }

    echo '</div>';


} elseif($do == 'Activate') {

    echo "<h1 class='text-center'> Activate Member </h1>";
    echo "<div class='container'>";

            $userid = isset($_GET['userid']) && is_numeric($_GET['userid']) ? intval($_GET['userid']) : 0;
            
            $check = checkItem('userid', 'users', $userid);

            if($check > 0) { 


                $stmt = $con->prepare("UPDATE users SET RegStatus = 1 WHERE UserID = ?");

                $stmt->execute(array($userid));

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Activated</div>';

                redirectHome($theMsg);

                

            } else {

                $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                redirectHome($theMsg);

            }

    echo '</div>';


}

include $tpl . 'footer.php';

} else {
    header('Location: index.php');
    exit();
}

ob_end_flush();

?>
    