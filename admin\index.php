<?php
session_start();

$noNavbar ='';

$pageTitle ='Login';

    header('Location: dashboard.php');   // redirect to dashboard page


// if (isset($_SESSION['Username'])) {
//     header('Location: dashboard.php');   // redirect to dashboard page

//  }


include 'init.php';
// include $tpl . 'header.php';
// include 'includes/languages/english.php';
// // include 'includes/languages/arabic.php';

// check if user coming from HTTP post request

if ($_SERVER['REQUEST_METHOD'] == 'POST'){
    $username = $_POST['user'];
    $password = $_POST['pass'];
    $hashedPass = sha1($password);

    // check if the user exist in database
    
    $stmt = $con->prepare("SELECT UserID, Username, Password FROM users 
    WHERE Username = ? AND Password = ? AND GroupID = 1 LIMIT 1");

    $stmt->execute(array($username, $hashedPass));
    $row = $stmt->fetch();
    $count = $stmt->rowCount();


    // if count > 0 this mean the database contains the record
if ($count > 0) {
    $_SESSION['Username'] = $username;   //register session name
    $_SESSION['ID'] = $row['UserID'];   //register session ID
    header('Location: dashboard.php');   // redirect to dashboard page
    exit();
} 
}

?>

<form class="login" action="<?php echo $_SERVER['PHP_SELF'] ?>" method="POST">
    <h4 class="text-center">Admin login</h4>
    <input class="form-control" type="text" name="user" placeholder="username" autocomplete="off" />
    <input class="form-control" type="password" name="pass" placeholder="password" autocomplete="new-password" />
    <input class="btn btn-primary btn-block" type="submit" value="login" />


</form>





<?php
include $tpl . 'footer.php';
?>


