
/* Start Main rulez */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: sans-serif;
}
p,h2{
    margin: 0;
}
.navbar{
    display: none;
}
body {
    /* background-color: #dbd8d8; */
    font-size: 16px;


    /* display: flex; */
    /* justify-content: center; */
    /* align-items: center; */
    /* min-height: 100vh; */
    background: #EAEAEA;
    display: flex;

}




.img-box{
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid white;
    flex-shrink: 0;
}
.img-box img{
    width: 100%;
}

.profile{
display: flex;
align-items: center;
gap: 30px;

}
.profile h2{
    font-size: 20px;
    text-transform: capitalize;
}


.menu{
    background-color: wheat;
    width: 95px;
    height: 100vh;
    padding: 20px;
    overflow: hidden;
    transition: .5s;
}
.menu:hover{
    width: 350px;
    /* transition: all 0.3s ease; */
}
ul{
    list-style: none;
    position: relative;
    height: 95%;
}
ul li a {
    display: block;
    text-decoration: none;
    text-transform: uppercase;
    padding: 10px;
    margin: 10px 0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 40px;
    color: darkblue;
    cursor: pointer;
    transition: all 0.3s ease;
}
ul li a:hover, .active, .data-info .box:hover, td:hover{
    background-color:sienna;
    text-decoration: none;

    color: #fff;
    transform: scale(1.05);
    transition: all 0.3s ease;
}
/* 
ul li i{
    font-size: 20px;
    color: #000;
}
ul li a{
    text-decoration: none;
    color: #000;
}
ul li a:hover{
    color: red
} */


.log-out{
    position: absolute;
    bottom: 0;
    width: 100%;
}
.log-out a {
    background-color: red;
}

ul li a i {
    font-size: 30px;
    color: #000;
}
.dashcontent{
    width: 100%;
    margin: 10px;
}
.dashpanel{
    background-color: rebeccapurple;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    margin: 10px 0;
    color: #fff;
}
.data-info{
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 10px;
}
.data-info .box {
    background-color: #123;
    height: 150px;
    flex-basis: 150px;
    flex-grow: 1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-around;
}
.data-info .box i{
    font-size: 40px;
    color: antiquewhite;
}
.data-info .box .data{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
}
.data-info .box .data p{
    font-size: 14px;
    color: #fff;
}
.data-info .box .data span{
    font-size: 30px;
    color: #fff;

}


table {
    width: 100%;
    text-align: center;

    border-collapse: separate;
    border-spacing: 5px 10px;
}

td, th{
    background-color: rgb(178, 174, 229);
    height: 40px;
    border-radius: 8px;
}

th {
    background-color: aqua;
    text-align: center;
}
.price, .count{
    padding: 6px;
    border-radius: 6px;
}
.price{
    background-color: green;
}
.count{
    background-color: goldenrod;
    color: black;
}






/* crud */

.crudscreen{
    width: 100vw;
    min-height: 100vh;
    /* display: flex; */
    /* justify-content: center; */
    /* align-items: center; */
    background: #222;
    color: #fff;
    font-family: system-ui;
    /* display: none; */
}
.crud{
    width: 80%;
    margin: auto;

}
.crud .head {
    text-align: center;
    text-transform: capitalize;
}



input {
    width: 100%;
    height: 30px;
    outline: none;
    border: none;
    background: #111;
    margin: 4px 0;
    border-radius: 4px;
    padding: 4px;
    color: #fff;
}
input:focus{
    background: #000;
    transform: scale(1.1);
}
.crudprice input{
    width: 20%;
}
#total{
    background: red;
    padding: 5px 2px;
    border-radius: 4px;
}
#total::before{
    content: 'Total : ';
}
button{
    width: 100%;
    height: 30px;
    border: none;
    cursor: pointer;
    background: #470053;
    color: #fff;
    border-radius: 20px;
    transition: 0.5s;
}
button:hover{
    background: #51025f;
    letter-spacing: 1px;
}
.btnSearch{
    display: flex;
    justify-content: space-between;
}
.btnSearch button {
    width: 45%;
}

#deleteAll{
    margin: 20px 0;
}

table button:hover{
    transform: scale(1.05);
}

table{
    width: 100%;
    text-align: center;
    margin: 10px 0;
}
table th{
    text-transform: capitalize;
}
th,td{
    padding: 5pxs;
}



























