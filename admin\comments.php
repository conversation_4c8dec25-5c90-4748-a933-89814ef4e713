<?php

// Manage members page

// you can Add | Edit | Delete members here 


session_start();
$pageTitle = 'Comments';
if(isset($_SESSION['Username'])) {
    
    include 'init.php';
    
    $do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

    // Start manage page 
    if($do == 'Manage') {// Manage members page

    $stmt = $con->prepare("SELECT 
                            comments.*, items.Name AS item_Name, users.Username AS Member
                        FROM 
                            comments
                        INNER JOIN
                            items
                        ON
                            items.Item_ID = comments.item_id
                        INNER JOIN
                            users
                        ON
                            users.UserID = comments.user_id
                        ORDER BY
                            c_id DESC
                        ");
    $stmt->execute();
    
    $comments = $stmt->fetchAll();

    if (! empty ($comments)) {

    ?>

    <h1 class="text-center"> Manage comments </h1>

    <div class="container">
        <div class="table-responsive">
            <table class="main-table text-center table table-boardered">
                <tr>
                    <td>#ID</td>
                    <td>Comment</td>
                    <td>Item name</td>
                    <td>User name</td>
                    <td>Added date</td>
                    <td>Control</td>
                </tr>
                <?php
                foreach($comments as $comment) {
                    echo "<tr>";
                        echo "<td>" . $comment['c_id'] . "</td>";
                        echo "<td>" . $comment['comment'] . "</td>";
                        echo "<td>" . $comment['item_Name'] . "</td>";
                        echo "<td>" . $comment['Member'] . "</td>";
                        echo "<td>" . $comment['comment_date'] . "</td>";
                        echo "<td>
                        <a href='comments.php?do=Edit&comid=" . $comment['c_id'] . "' class='btn btn-success'><i class='fa fa-edit'></i> Edit </a>
                        <a href='comments.php?do=Delete&comid=" . $comment['c_id'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Delete </a>";
                        
                        if ($comment['status'] == 0){
                            echo "<a href='comments.php?do=Approve&comid=" . $comment['c_id'] . "' class='btn btn-info activate'><i class='fa fa-close'></i> Approve </a>";
                    
                        }


                        echo "</td>";
                    echo "</tr>";
                    
                }
              
                ?>
            </table>
        </div>
    </div>
    <?php } else {

    echo '<div class="container">';
    echo '<div class="nice-message"> there is no comments to show </div>';
    echo '</div>';

    }

    ?>

<?php


} elseif ($do == 'Edit') {  // Edit page 


    $comid = isset($_GET['comid']) && is_numeric($_GET['comid']) ? intval($_GET['comid']) : 0;
    
     
    $stmt = $con->prepare("SELECT * 
                            FROM comments 
                            WHERE c_id = ? 
                            ");
    $stmt->execute(array($comid));
    $row = $stmt->fetch();
    $count = $stmt->rowCount();

    if($stmt->rowCount() > 0) { ?>

            <h1 class="text-center"> Edit comment </h1>

            <div class="container">
                <form class="form-horizontal" action="?do=Update" method="POST">
                <input type="hidden" name="comid" value="<?php echo $comid ?>"/>

                    <!-- start comment field  -->
                    <div class="form-group form-group-lg">
                        <label class="col-sm-2 control-label"> comment </label>
                        <div class="col-sm-10 col-md-4">
                            <textarea class="form-control" name="comment"><?php echo $row['comment'] ?></textarea>
                        
                    
                        </div>
                    </div>
                    <!-- End comment field  -->
                    
                    
                    
                    
                    
                    <!-- start Submit/Save btn  -->
                    <div class="form-group  form-group-lg">
                        <div class="col-sm-offset-2 col-sm-10">
                            <input type="submit" value="Save" class="btn btn-primary btn-lg" />
                        </div>
                    </div>
                    <!-- End Submit/Save btn  -->

                </form>
            </div>

    <?php
    

    } else {

        echo "<div class='container'>";
    
        $theMsg = '<div class="alert alert-danger">there is no such ID</div>';

        redirectHome($theMsg);

        echo  "</div>";
    }


} elseif ($do == 'Update') { // update page

    echo "<h1 class='text-center'> Update Comment </h1>";
    echo "<div class='container'>";

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {

        // get variables from from 
        $comid     = $_POST['comid'];
        $comment   = $_POST['comment'];
        
        
        

        // check if no error update database 

        if (empty($formErrors)) {
            
        // update database 
        
        $stmt = $con->prepare("UPDATE comments SET comment  = ?
                                WHERE c_id = ?");

        $stmt->execute(array($comment, $comid));

        // // Echo success message

        $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

        redirectHome($theMsg, 'back');


        }


    } else {

        $theMsg ='<div class="alert alert-danger">sorry you can not browse this page directly</div>';

        redirectHome($theMsg);

    }

    echo "</div>";


} elseif ($do == 'Delete') {  // Delete member page


    
    echo "<h1 class='text-center'> Delete Comment </h1>";
    echo "<div class='container'>";

            $comid = isset($_GET['comid']) && is_numeric($_GET['comid']) ? intval($_GET['comid']) : 0;
            
            $check = checkItem('c_id', 'comments', $comid);

            if($check > 0) { 


                $stmt = $con->prepare("DELETE FROM comments WHERE c_id = :zid");

                $stmt->bindParam(":zid", $comid);

                $stmt->execute();

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

                redirectHome($theMsg, 'back');

                

            } else {

                $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                redirectHome($theMsg);

            }

    echo '</div>';


} elseif($do == 'Approve') {

    echo "<h1 class='text-center'> Activate Comment </h1>";
    echo "<div class='container'>";

            $comid = isset($_GET['comid']) && is_numeric($_GET['comid']) ? intval($_GET['comid']) : 0;
            
            $check = checkItem('c_id', 'comments', $comid);

            if($check > 0) { 


                $stmt = $con->prepare("UPDATE comments SET status = 1 WHERE c_id = ?");

                $stmt->execute(array($comid));

                $theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record approved</div>';

                redirectHome($theMsg, 'back');

                

            } else {

                $theMsg = '<div class="alert alert-danger">This id is not exist</div>';
                redirectHome($theMsg);

            }

    echo '</div>';


}




    include $tpl . 'footer.php';

} else {

    header('Location: index.php');
    
    exit();
}
