console.log('Welcome to the admin panel');


$(function () {

    'use strict';

    // Lunch the select box script/plugin

    $("select").selectBoxIt({
        autoWidth: false,
    });


    // Dashboard

    $('.toggle-info').click(function () {
        $(this).toggleClass('selected').parent().next('.panel-body').fadeToggle(100);

        if ($(this).hasClass('selected')) {

            $(this).html('<i class="fa fa-minus fa-lg"></i>');
        } else {

            $(this).html('<i class="fa fa-plus fa-lg"></i>');
        }


    });


    // Hide Placeholder On Form Focus

    $('[placeholder]').focus(function () {

        $(this).attr('data-text', $(this).attr('placeholder'));

        $(this).attr('placeholder', '');

    }).blur(function () {

        $(this).attr('placeholder', $(this).attr('data-text'));

    });

    // Add Asterisk on required fiels

    $('input').each(function () {

        if ($(this).attr('required') === 'required') {

            $(this).after('<span class="asterisk">*</span>');

        }
    });


    // Convert password field to text field on hover

    var passField = $('.password');


    $('.show-pass').hover(function () {

        passField.attr('type', 'text');

    }, function () {
        passField.attr('type', 'password');

    });

    // Confirmation message on button

    $('.confirm').click(function () {

        return confirm('Are you sure you want to delete this member ?');

    });

    // Category view options

    $('.cat h3').click(function () {

        $(this).next('.full-view').fadeToggle(200);

    });

    $('.option span').click(function () {

        $(this).addClass('active').siblings('span').removeClass('active');

        if ($(this).data('view') === 'full') {
            $('.cat .full-view').fadeIn(200);

        } else {
            $('.cat .full-view').fadeOut(200);

        }

    });

    // Show Delete button

    $('.child-link').hover(function () {

        $(this).find('.show-delete').fadeIn(400);

    }, function () {

        $(this).find('.show-delete').fadeOut(400);

    });



    // item upload picture - no file choosen
    // var upload = document.querySelector('input');
    // var upicon = document.querySelector('.upload');
    // var close = document.querySelector('.icon .close');

    // var loadFile = function(event){
    //     var image = document.querySelector('.itemimg img');
    //     image.classList.add('active');
    //     close.classList.add('exit');
    //     image.src = URL.createObjectURL(event.target.files[0])

    //     close.onclick =(e) =>{
    //         image.classList.remove('active');
    //         close.classList.remove('exit');
    //     }
    // }

    
    // item upload picture - no file choosen

    // const form = document.querySelector("form"),
    // fileInput = document.querySelector(".file-input"),
    // progressArea = document.querySelector(".progress-area"),
    // uploadedArea = doc.querySelector(".uploaded-area");

    // form.addEventListener("click",() => {
    //     fileInput.click();
    // })
    // fileInput.onchange = ({ target }) => {
    //     let file = target.file[0];
    //     if(file){
    //         let fileName = file.name;
    //         if(fileName.length >=12){
    //             let splitName = fileName.splitName.split(".");
    //             fileName = splitName[0].substring(0, 13) + " ...." + splitName[1]
    //         }

    //         uploadFile(fileName);

    //     }
    // };
    // function uploadFile(name){
    //     let xhr = new XMLHttpRequest();
    //     xhr.open("POST", "php/upload");
    //     xhr.upload.addEventListener("progress", ({loaded, total}) => {

    //         let fileLoaded = Math.floor((loaded / total) * 100);
    //         let fileTotal = Math.floor(total / 1000);
    //         let fileSize;
    //         fileTotal < 4048
    //         ? (fileSize = fileTotal + " KB")
    //         : (fileSize = (loaded / (1024 * 1024)).toFixed(2) * " MB");
    //         let progressHTML = ` <li class="row">
    //         <i class="fas fa-file-alt"></i>
    //         <div class="content">
    //             <div class="details">
    //                 <span class="name">${name} * Uploading</span>
    //                 <span class="percent">${fileLoaded}%</span>
    //             </div>
    //             <div class="progress-bar">
    //                 <div class="progress" style="width: ${fileLoaded}%"></div>
    //             </div>
    //         </div>
    //         </li>

            
            
    //         `;
    //         uploadedArea.classList.add("onprogress");
    //         progressArea.innerHTML = progressHTML;
    //         if(loaded == total){
    //             progressArea.innerHTML = "";
    //             let uploadedHTML = `
                
    //             <li class="row">
    //             <div class="content upload">
    //             <i class="fas fa-file-alt"></i>
    //             <div class="details">
    //             <span class="name">${name} * uploaded</span>
    //             <span class="size">${fileSize}</span>
    //             </div>
    //             </div>
    //             <i class="fas fa-check></i>
    //             </li>
                
    //             `
    //         }
    //     });
    //     let data = new FormData(form);
    //     xhr.send(data);
    // }



});








// upload product profile picture  -- Image 1

// let prodfilePic = document.getElementById("prod-pic");
// let inputFile = document.getElementById("inputpic");

// inputFile.onchange =function(){
//     prodfilePic.src = URL.createObjectURL(inputFile.files[0]);
// }


// upload product profile picture  -- Image 2

// let prodfilePicB = document.getElementById("prodpicB");
// let inputFileB = document.getElementById("inputpicB");

// inputFileB.onchange =function(){
//     prodfilePicB.src = URL.createObjectURL(inputFileB.files[0]);
// }


// // upload product profile picture  -- Image 3

// let prodfilePicC = document.getElementById("prodpicC");
// let inputFileC = document.getElementById("inputpicC");

// inputFileC.onchange =function(){
//     prodfilePicC.src = URL.createObjectURL(inputFileC.files[0]);
// }

// upload product profile picture  -- Image 4

// let prodfilePicD = document.getElementById("prodpicD");
// let inputFileD = document.getElementById("inputpicD");

// inputFileD.onchange =function(){
//     prodfilePicD.src = URL.createObjectURL(inputFileD.files[0]);
// }

// // upload product profile picture  -- Image 5

// let prodfilePicE = document.getElementById("prodpicE");
// let inputFileE = document.getElementById("inputpicE");

// inputFileE.onchange =function(){
//     prodfilePicE.src = URL.createObjectURL(inputFileE.files[0]);
// }

// // upload Avatar profile picture  

// let prodfilePicF = document.getElementById("prodpicF");

// console.log(prodfilePicF);

// let inputFileF = document.getElementById("inputpicF");



// inputFileF.onchange =function(){
//     prodfilePicF.src = URL.createObjectURL(inputFileF.files[0]);
// }

// // preview images


// document.addEventListener('DOMContentLoaded', function() {
//     // Image preview functionality
//     const imageInputs = document.querySelectorAll('.image-input');
    
//     imageInputs.forEach(input => {
//         input.addEventListener('change', function() {
//             const previewId = this.getAttribute('data-preview');
//             const preview = document.getElementById(previewId);
            
//             if (this.files && this.files[0]) {
//                 const reader = new FileReader();
                
//                 reader.onload = function(e) {
//                     preview.src = e.target.result;
//                 }
                
//                 reader.readAsDataURL(this.files[0]);
//             }
//         });
//     });
// });







// crud 
// crud 
// crud 
// crud 

let title = document.getElementById('title');
let price = document.getElementById('price');
let taxes = document.getElementById('taxes');
let ads = document.getElementById('ads');
let discount = document.getElementById('discount');
let total = document.getElementById('total');
let quanty = document.getElementById('quanty');
let category = document.getElementById('category');
let submit = document.getElementById('submit');

let mood = 'create';
let currentId;



// get total 
function getTotal() {
    if(price.value != ''){
        let result = (+price.value + +taxes.value + +ads.value) - +discount.value;
        total.innerHTML = result;
        total.style.background = '#040';
    }else{
        total.innerHTML = '';
        total.style.background = '#e00d0d';
    } 
}



// create product s

let dataProd ;
if(localStorage.product != null){
    dataProd = JSON.parse(localStorage.product);
}else{
    dataProd = [];
}

submit.onclick = function(){
    let newProd = {
        title: title.value.toLowerCase(),
        price: price.value,
        taxes: taxes.value,
        ads: ads.value,
        discount: discount.value,
        total: total.innerHTML,
        quanty: quanty.value,
        category: category.value.toLowerCase(),
    }

    if(title.value != '' && price.value != '' && category.value != '' && newProd.quanty < 10){

            if(mood == 'create'){
                
                if(newProd.quanty > 1){
                    for(let i = 0; i < newProd.quanty; i++){
                        dataProd.push(newProd);
                    }
                }else{
                    dataProd.push(newProd);
                } 
                
            }else{
                dataProd[currentId] = newProd;
                mood = 'create';
                submit.innerHTML = 'create';
                quanty.style.display = 'block';
                // currentId = '';
        }
        clearData();
    }

    // save local storage
    localStorage.setItem('product', JSON.stringify(dataProd)); // set localstorage file name and location

    // console.log(dataProd) 


    showData();
    // alert('Product added successfully')
}






// clear inputs

function clearData(){
    title.value = '';
    price.value = '';
    taxes.value = '';
    ads.value = '';
    discount.value = '';
    quanty.value = '';
    category.value = '';
    total.innerHTML = '';
}



// read data from local storage

function showData(){

    getTotal();

    let table = '';

    for(let i = 0; i < dataProd.length; i++){

        table += `
        <tr>
            <td>${i+1}</td>
            <td>${dataProd[i].title}</td>
            <td>${dataProd[i].price}</td>
            <td>${dataProd[i].taxes}</td>
            <td>${dataProd[i].ads}</td>
            <td>${dataProd[i].discount}</td>
            <td>${dataProd[i].total}</td>
            <td>${dataProd[i].category}</td>
            <td><button onclick="updateData(${i})" id="update">Update</button></td>
            <td><button onclick="deleteItem(${i})" id="delete">Delete</button></td>
        </tr>
`
    }

    document.getElementById('tableBody').innerHTML = table;

    let btnDeleteAll = document.getElementById('deleteAll');

    if(dataProd.length> 0) {

        btnDeleteAll.innerHTML = `
        <button onclick="deleteAll()">Delete All (${dataProd.length})</button>
        `;
    } else {
        btnDeleteAll.innerHTML = '';
    }
}

showData();




// delete product

        // delete one product
function deleteItem(i){

    dataProd.splice(i, 1);
    localStorage.product = JSON.stringify(dataProd);
    showData();
}


        // delete all product
function deleteAll() {
    localStorage.clear();
    dataProd.splice(0, dataProd.length);
    showData();
}




// count -create many product per click




// update product

function updateData(i) {
    title.value = dataProd[i].title;
    price.value = dataProd[i].price;
    taxes.value = dataProd[i].taxes;
    ads.value = dataProd[i].ads;
    discount.value = dataProd[i].discount;

    getTotal();
    quanty.style.display = 'none';
    category.value = dataProd[i].category;

    submit.innerHTML = 'Update';
    mood = 'update';
    currentId = i;
    scroll({top:0, behavior: 'smooth'});
    // submit.onclick = 'updateProd()';
}

// search product

let searchMood = 'title';

function getSearchMood(id){
    let search = document.getElementById('search');

    if(id == 'searchTitle'){
        searchMood = 'title';
        search.placeholder = 'Search by title';
    }else{
        searchMood = 'category';
        search.placeholder = 'Search by category';
    }
    search.focus();
    search.value = '';
    showData();

}

function searchProd(value){

    let table = '';
        for(let i = 0; i < dataProd.length; i++){

         if(searchMood == 'title'){

            
            if(dataProd[i].title.includes(value.toLowerCase())){
                
              table += `
                <tr>
                    <td>${i}</td>
                    <td>${dataProd[i].title}</td>
                    <td>${dataProd[i].price}</td>
                    <td>${dataProd[i].taxes}</td>
                    <td>${dataProd[i].ads}</td>
                    <td>${dataProd[i].discount}</td>
                    <td>${dataProd[i].total}</td>
                    <td>${dataProd[i].category}</td>
                    <td><button onclick="updateData(${i})" id="update">Update</button></td>
                    <td><button onclick="deleteItem(${i})" id="delete">Delete</button></td>
                </tr>
                `;

                }

            }else{

                if(dataProd[i].category.includes(value.toLowerCase())){
                    
                table += `
                    <tr>
                        <td>${i}</td>
                        <td>${dataProd[i].title}</td>
                        <td>${dataProd[i].price}</td>
                        <td>${dataProd[i].taxes}</td>
                        <td>${dataProd[i].ads}</td>
                        <td>${dataProd[i].discount}</td>
                        <td>${dataProd[i].total}</td>
                        <td>${dataProd[i].category}</td>
                        <td><button onclick="updateData(${i})" id="update">Update</button></td>
                        <td><button onclick="deleteItem(${i})" id="delete">Delete</button></td>
                    </tr>
                    `;

                    }
                }
        }
        document.getElementById('tableBody').innerHTML = table;


}


















// clean data 