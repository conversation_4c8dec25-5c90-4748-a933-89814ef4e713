<?php
ob_start();
session_start();
    include 'init.php';

    $pageTitle ='Dashboard';
  

//   start dhashboard page 

 $numUsers = 6;   // number of latesT users 
 
 $latestUsers = getLatest("*", "users", "UserID", $numUsers);
 
 $numItems = 6;    // number of latesT items

 $latestItems = getLatest("*", "items", "Item_ID", $numItems);

 $numComments = 6;
 

?>





<div class="menu">
    <ul>
        <li class="profile">
            <div class="img-box">
                <img src="img/user.webp" alt="profile picture">
            </div>
            <h2>Name</h2>
        </li>

        <li>
            <a class="active" href="#">
                <i class="fas fa-home"></i>
                <p>Dashboard</p>
            </a>
        </li>


        <li>
            <a href="#">
                <i class="fas fa-user-group"></i>
                <p>Clients</p>
            </a>
        </li>


        <li>
            <a href="#">
                <i class="fas fa-table"></i>
                <p>Products</p>
            </a>
        </li>


        <li>
            <a href="#">
                <i class="fas fa-chart-pie"></i>
                <p>Chart</p>
            </a>
        </li>


        <li>
            <a href="#">
                <i class="fas fa-pen"></i>
                <p>Posts</p>
            </a>
        </li>


        <li>
            <a href="#">
                <i class="fas fa-star"></i>
                <p>Favorites</p>
            </a>
        </li>



        <li>
            <a href="#">
                <i class="fas fa-cog"></i>
                <p>Settings</p>
            </a>
        </li>


        <li class="log-out">
            <a href="#">
                <i class="fas fa-sign-out"></i>
                <p>Log out</p>
            </a>
        </li>



    </ul>
</div>

<div class="dashcontent">

    <div class="dashpanel"> 
        <p>Dashboard</p>
        <i class="fas fa-chart-bar"></i>
    </div>

    <div class="data-info">

        <div class="box">
            <i class="fas fa-user"></i>
            <div class="data">
                <p>Users</p>
                <span>100</span>
            </div>
        </div>


        
        <div class="box">
            <i class="fas fa-pen"></i>
            <div class="data">
                <p>Posts</p>
                <span>51</span>
            </div>
        </div>


        
        <div class="box">
            <i class="fas fa-table"></i>
            <div class="data">
                <p>Products</p>
                <span>3521</span>
            </div>
        </div>


        
        <div class="box">
            <i class="fas fa-dollar"></i>
            <div class="data">
                <p>Revenue</p>
                <span>$5320S</span>
            </div>
        </div>
    </div>

    
    <div class="dashpanel"> 
        <p>Products</p>
        <i class="fas fa-table"></i>
    </div>

    <table>

        <thead>
            <tr>
                <th>Product Name</th>
                <th>Price</th>
                <th>Count</th>
            </tr>
        </thead>
    
    
        <tbody>
            <tr>
                <td>TV</td>
                <td><span class="price">$124</span></td>
                <td><span class="count">6</span></td>
            </tr>
            
            <tr>
                <td>Mobile</td>
                <td><span class="price">$325</span></td>
                <td><span class="count">16</span></td>
            </tr>



            <tr>
                <td>Laptop</td>
                <td><span class="price">$2124</span></td>
                <td><span class="count">11</span></td>
            </tr>




            <tr>
                <td>Playstation</td>
                <td><span class="price">$4124</span></td>
                <td><span class="count">6</span></td>
            </tr>







            <tr>
                <td>Switch</td>
                <td><span class="price">$75</span></td>
                <td><span class="count">2</span></td>
            </tr>





            <tr>
                <td>TV</td>
                <td><span class="price">$124</span></td>
                <td><span class="count">6</span></td>
            </tr>
        </tbody>
    </table>



</div>















<?php

// end dashboard page 


    include $tpl . 'footer.php';
 

 ob_end_flush();

 ?>